// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة القائمة المتنقلة
    initMobileMenu();

    // تهيئة الوضع المظلم
    initDarkMode();

    // تهيئة العدادات
    initCounters();

    // تهيئة الأحداث للأزرار
    initButtonEvents();

    // تهيئة نموذج البحث
    initSearchForm();

    // تهيئة الرسوم البيانية إذا كانت موجودة
    if (typeof Chart !== 'undefined') {
        initCharts();
    }
});

// تهيئة القائمة المتنقلة
function initMobileMenu() {
    const menuToggle = document.getElementById('menuToggle');
    const navLinks = document.querySelector('.nav-links');

    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', function() {
            navLinks.classList.toggle('active');
            this.classList.toggle('active');

            // تحديث أزرار البحث والملف الشخصي
            const searchBtn = document.getElementById('searchBtn');
            const profileBtn = document.getElementById('profileBtn');

            if (searchBtn) {
                searchBtn.classList.toggle('hidden', navLinks.classList.contains('active'));
            }

            if (profileBtn) {
                profileBtn.classList.toggle('hidden', navLinks.classList.contains('active'));
            }
        });

        // إضافة دعم للقوائم المنسدلة على جميع الأجهزة
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                // منع الانتقال إلى الرابط عند النقر على زر القائمة المنسدلة
                e.preventDefault();

                if (window.innerWidth <= 992) {
                    // إغلاق جميع القوائم المنسدلة الأخرى
                    const allDropdowns = document.querySelectorAll('.dropdown');
                    allDropdowns.forEach(dropdown => {
                        if (dropdown !== this.parentElement) {
                            dropdown.classList.remove('mobile-active');
                        }
                    });

                    // تبديل حالة القائمة المنسدلة الحالية
                    this.parentElement.classList.toggle('mobile-active');
                }
            });
        });

        // إضافة تأثيرات متقدمة للقوائم المنسدلة
        const dropdowns = document.querySelectorAll('.dropdown');
        dropdowns.forEach(dropdown => {
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            if (dropdownMenu) {
                // إضافة تأثير ظهور تدريجي للعناصر
                const menuItems = dropdownMenu.querySelectorAll('a');
                menuItems.forEach((item, index) => {
                    item.style.transitionDelay = `${index * 0.05}s`;
                });
            }
        });

        // إغلاق القائمة المتنقلة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!navLinks.contains(e.target) && !menuToggle.contains(e.target)) {
                navLinks.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });

        // إضافة تأثير تمييز القائمة النشطة
        highlightActiveNavItem();

        // إضافة معالج للنقر على روابط القائمة المنسدلة
        document.querySelectorAll('.dropdown-menu a').forEach(link => {
            // إزالة أي مستمعات حدث سابقة
            link.removeEventListener('click', handleDropdownLinkClick);
            // إضافة مستمع حدث جديد
            link.addEventListener('click', handleDropdownLinkClick);
        });

        // وظيفة معالجة النقر على روابط القائمة المنسدلة
        function handleDropdownLinkClick(e) {
            // الحصول على الرابط
            const href = this.getAttribute('href');

            // إذا كان الرابط يشير إلى #، منع السلوك الافتراضي
            if (href === '#') {
                e.preventDefault();
                return;
            }

            // للروابط الأخرى، السماح بالسلوك الافتراضي
            // لا نحتاج لمنع السلوك الافتراضي هنا
        }
    }
}

// تمييز عنصر القائمة النشط بناءً على الصفحة الحالية
function highlightActiveNavItem() {
    const currentPage = window.location.pathname.split('/').pop();

    // تحديد جميع روابط القائمة
    const navLinks = document.querySelectorAll('.nav-links a');

    navLinks.forEach(link => {
        // إزالة الفئة النشطة من جميع الروابط
        link.classList.remove('active');

        // الحصول على مسار الرابط
        const linkPath = link.getAttribute('href');

        // إذا كان الرابط يشير إلى الصفحة الحالية، أضف فئة نشطة
        if (linkPath === currentPage ||
            (currentPage === '' && linkPath === 'index.html') ||
            (currentPage === 'index.html' && linkPath === 'index.html')) {
            link.classList.add('active');

            // إذا كان الرابط داخل قائمة منسدلة، قم بتمييز الزر المنسدل أيضًا
            const parentDropdown = link.closest('.dropdown');
            if (parentDropdown) {
                const dropdownToggle = parentDropdown.querySelector('.dropdown-toggle');
                if (dropdownToggle) {
                    dropdownToggle.classList.add('active');
                }
            }
        }
    });
}

// تهيئة الوضع المظلم
function initDarkMode() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;

    // التحقق من وجود تفضيل الوضع المظلم في التخزين المحلي
    const isDarkMode = localStorage.getItem('darkMode') === 'true';

    // تطبيق الوضع المظلم إذا كان مفعلاً
    if (isDarkMode) {
        body.classList.add('dark-mode');
        updateDarkModeIcon(true);
    }

    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            // تبديل حالة الوضع المظلم
            const isCurrentlyDark = body.classList.toggle('dark-mode');

            // تحديث الأيقونة
            updateDarkModeIcon(isCurrentlyDark);

            // حفظ التفضيل في التخزين المحلي
            localStorage.setItem('darkMode', isCurrentlyDark);
        });
    }
}

// تحديث أيقونة الوضع المظلم
function updateDarkModeIcon(isDark) {
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        const icon = darkModeToggle.querySelector('i');
        if (icon) {
            if (isDark) {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }
        }
    }
}

// تهيئة العدادات
function initCounters() {
    // الحصول على الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // حساب عدد الموظفين حسب الوصف الوظيفي
    const teachingEmployees = employees.filter(emp => emp.jobDescription === 'teaching');
    const technicalEmployees = employees.filter(emp => emp.jobDescription === 'technical');
    const administrativeEmployees = employees.filter(emp => emp.jobDescription === 'administrative');

    // الحصول على البيانات الفعلية من التخزين المحلي
    const stats = {
        // عدد الموظفين
        employeeCount: employees.length,

        // عدد التدريسيين
        teachingCount: teachingEmployees.length,

        // عدد الفنيين
        technicalCount: technicalEmployees.length,

        // عدد الإداريين
        administrativeCount: administrativeEmployees.length,

        // عدد كتب الشكر
        appreciationCount: JSON.parse(localStorage.getItem('thanks') || '[]').length,

        // عدد العقوبات
        penaltyCount: JSON.parse(localStorage.getItem('penalties') || '[]').length,

        // عدد الإجازات الحالية (الموظفين في إجازة حالياً)
        leaveCount: JSON.parse(localStorage.getItem('employeeLeaves') || '[]').filter(leave => {
            const today = new Date();
            const startDate = new Date(leave.startDate);
            const endDate = new Date(leave.endDate);
            return startDate <= today && endDate >= today;
        }).length,

        // عدد مواقع العمل
        locationCount: JSON.parse(localStorage.getItem('workLocations') || '[]').length,

        // عدد العناوين الوظيفية
        jobTitleCount: (() => {
            const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '{}');
            // جمع عدد العناوين الوظيفية من جميع الفئات
            let count = 0;
            for (const category in jobTitles) {
                if (Array.isArray(jobTitles[category])) {
                    count += jobTitles[category].length;
                }
            }
            return count;
        })()
    };

    // استخدام قيم افتراضية إذا كانت البيانات فارغة (للعرض التوضيحي)
    const defaultStats = {
        employeeCount: 0,
        teachingCount: 0,
        technicalCount: 0,
        administrativeCount: 0,
        appreciationCount: 0,
        penaltyCount: 0,
        leaveCount: 0,
        locationCount: 0,
        jobTitleCount: 0
    };

    // تحديث العدادات بتأثير العد
    animateCounter('employeeCount', stats.employeeCount || defaultStats.employeeCount);
    animateCounter('teachingCount', stats.teachingCount || defaultStats.teachingCount);
    animateCounter('technicalCount', stats.technicalCount || defaultStats.technicalCount);
    animateCounter('administrativeCount', stats.administrativeCount || defaultStats.administrativeCount);
    animateCounter('appreciationCount', stats.appreciationCount || defaultStats.appreciationCount);
    animateCounter('penaltyCount', stats.penaltyCount || defaultStats.penaltyCount);
    animateCounter('leaveCount', stats.leaveCount || defaultStats.leaveCount);
    animateCounter('locationCount', stats.locationCount || defaultStats.locationCount);
    animateCounter('jobTitleCount', stats.jobTitleCount || defaultStats.jobTitleCount);
}

// تحريك العداد
function animateCounter(elementId, targetValue) {
    const element = document.getElementById(elementId);
    if (!element) return;

    let currentValue = 0;
    const duration = 2000; // مدة التحريك بالمللي ثانية
    const stepTime = 50; // الوقت بين كل خطوة بالمللي ثانية
    const steps = duration / stepTime;
    const increment = targetValue / steps;

    const timer = setInterval(function() {
        currentValue += increment;

        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }

        element.textContent = Math.round(currentValue);
    }, stepTime);
}

// تهيئة الأحداث للأزرار
function initButtonEvents() {
    // إضافة تأثير النقر على جميع الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إنشاء تأثير تموج عند النقر
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);

            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size / 2}px`;
            ripple.style.top = `${e.clientY - rect.top - size / 2}px`;

            // إزالة تأثير التموج بعد انتهاء التحريك
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // تم تعطيل مستمعات الأحداث لأزرار الشريط العلوي
    /*
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // تركيز على حقل البحث
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                // التمرير إلى حقل البحث
                searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }

    const profileBtn = document.getElementById('profileBtn');
    if (profileBtn) {
        profileBtn.addEventListener('click', function() {
            // يمكن إضافة منطق لعرض قائمة الملف الشخصي هنا
            alert('مرحباً بك في نظام إدارة العلاوات والترفيعات');
        });
    }
    */

    // إضافة تأثير التحويم على بطاقات الميزات
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px)';
        });
    });

    // ربط أزرار الاستكشاف بالصفحات المناسبة
    const exploreButtons = document.querySelectorAll('.feature-card .btn');
    if (exploreButtons.length >= 3) {
        // زر إدارة الموظفين
        exploreButtons[0].addEventListener('click', function() {
            window.location.href = 'employees-list.html';
        });

        // زر العلاوات والترفيعات
        exploreButtons[1].addEventListener('click', function() {
            window.location.href = 'allowance-reports.html';
        });

        // زر التقارير والإحصائيات
        exploreButtons[2].addEventListener('click', function() {
            window.location.href = 'promotion-reports.html';
        });
    }

    // تحديث التنبيهات الأخيرة والموظفين الجدد
    updateLatestAlerts();
    updateNewEmployees();
}

// تحديث التنبيهات الأخيرة
function updateLatestAlerts() {
    const alertsContainer = document.querySelector('.dashboard-section:first-child .section-header + div');
    if (!alertsContainer) return;

    // مسح التنبيهات الافتراضية
    alertsContainer.innerHTML = '';

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const today = new Date();

    // إنشاء مصفوفة للتنبيهات
    const alerts = [];

    // إضافة تنبيهات العلاوات المستحقة
    employees.forEach(employee => {
        if (employee.nextAllowanceDate) {
            const nextAllowanceDate = new Date(employee.nextAllowanceDate);
            const diffTime = nextAllowanceDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays <= 30 && diffDays >= 0) {
                alerts.push({
                    type: 'allowance',
                    title: 'علاوة مستحقة',
                    message: `الموظف ${employee.name} مستحق للعلاوة السنوية`,
                    date: employee.nextAllowanceDate,
                    priority: diffDays // كلما كان الرقم أقل، كانت الأولوية أعلى
                });
            }
        }
    });

    // إضافة تنبيهات الترفيعات المستحقة
    employees.forEach(employee => {
        if (employee.nextPromotionDate) {
            const nextPromotionDate = new Date(employee.nextPromotionDate);
            const diffTime = nextPromotionDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays <= 30 && diffDays >= 0) {
                alerts.push({
                    type: 'promotion',
                    title: 'ترفيع مستحق',
                    message: `الموظف ${employee.name} مستحق للترفيع الوظيفي`,
                    date: employee.nextPromotionDate,
                    priority: diffDays
                });
            }
        }
    });

    // إضافة تنبيهات الإحالة للتقاعد
    employees.forEach(employee => {
        if (employee.retirementDate) {
            const retirementDate = new Date(employee.retirementDate);
            const diffTime = retirementDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays <= 90 && diffDays >= 0) {
                alerts.push({
                    type: 'retirement',
                    title: 'إحالة للتقاعد',
                    message: `الموظف ${employee.name} سيتم إحالته للتقاعد قريباً`,
                    date: employee.retirementDate,
                    priority: diffDays
                });
            }
        }
    });

    // ترتيب التنبيهات حسب الأولوية (الأقرب أولاً)
    alerts.sort((a, b) => a.priority - b.priority);

    // عرض أحدث 3 تنبيهات
    const alertsToShow = alerts.slice(0, 3);

    if (alertsToShow.length === 0) {
        // إذا لم تكن هناك تنبيهات، عرض رسالة
        alertsContainer.innerHTML = `
            <div class="alert blue-alert">
                <div class="alert-header">
                    <h3><i class="fas fa-info-circle"></i> لا توجد تنبيهات</h3>
                </div>
                <p>لا توجد تنبيهات حالية في النظام</p>
            </div>
        `;
    } else {
        // عرض التنبيهات
        alertsToShow.forEach(alert => {
            let alertClass = 'blue-alert';
            let alertIcon = 'fas fa-info-circle';

            if (alert.type === 'allowance') {
                alertClass = 'blue-alert';
                alertIcon = 'fas fa-info-circle';
            } else if (alert.type === 'promotion') {
                alertClass = 'green-alert';
                alertIcon = 'fas fa-arrow-up';
            } else if (alert.type === 'retirement') {
                alertClass = 'amber-alert';
                alertIcon = 'fas fa-user-clock';
            }

            const alertElement = document.createElement('div');
            alertElement.className = `alert ${alertClass}`;
            alertElement.innerHTML = `
                <div class="alert-header">
                    <h3><i class="${alertIcon}"></i> ${alert.title}</h3>
                    <span class="alert-badge">${alert.type === 'allowance' ? 'علاوة' : alert.type === 'promotion' ? 'ترفيع' : 'تقاعد'}</span>
                </div>
                <p>${alert.message}</p>
                <span class="date"><i class="fas fa-clock"></i> ${formatDate(alert.date)}</span>
            `;

            alertsContainer.appendChild(alertElement);
        });
    }

    // تحديث رابط "عرض الكل"
    const viewAllLink = document.querySelector('.dashboard-section:first-child .view-all');
    if (viewAllLink) {
        if (alerts.length > 0) {
            viewAllLink.href = alerts[0].type === 'allowance' ? 'allowance-alerts.html' :
                              alerts[0].type === 'promotion' ? 'promotion-alerts.html' : 'retirement-alerts.html';
        } else {
            viewAllLink.href = 'allowance-alerts.html';
        }
    }
}

// تحديث الموظفين الجدد
function updateNewEmployees() {
    const employeesContainer = document.querySelector('.dashboard-section:nth-child(2) .section-header + div');
    if (!employeesContainer) return;

    // مسح الموظفين الافتراضيين
    employeesContainer.innerHTML = '';

    // الحصول على الموظفين
    let employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // ترتيب الموظفين حسب تاريخ التعيين (الأحدث أولاً)
    employees.sort((a, b) => new Date(b.hireDate) - new Date(a.hireDate));

    // عرض أحدث 3 موظفين
    const newEmployees = employees.slice(0, 3);

    if (newEmployees.length === 0) {
        // إذا لم يكن هناك موظفين، عرض رسالة
        employeesContainer.innerHTML = `
            <div class="employee-card">
                <div class="employee-info">
                    <div class="employee-avatar">-</div>
                    <div class="employee-name-title">
                        <h3>لا يوجد موظفين</h3>
                        <p>لم يتم إضافة موظفين بعد</p>
                    </div>
                </div>
            </div>
        `;
    } else {
        // عرض الموظفين
        newEmployees.forEach(employee => {
            // استخراج الأحرف الأولى من اسم الموظف للصورة الرمزية
            const nameParts = employee.name.split(' ');
            const initials = nameParts.length >= 2 ?
                `${nameParts[0].charAt(0)} ${nameParts[1].charAt(0)}` :
                employee.name.charAt(0);

            const employeeElement = document.createElement('div');
            employeeElement.className = 'employee-card';
            employeeElement.innerHTML = `
                <div class="employee-info">
                    <div class="employee-avatar">${initials}</div>
                    <div class="employee-name-title">
                        <h3>${employee.name}</h3>
                        <p>${employee.jobTitle || '-'}</p>
                    </div>
                </div>
                <div class="employee-details">
                    <p>${employee.workLocation || '-'}</p>
                    <p class="date">تاريخ التعيين: ${formatDate(employee.hireDate)}</p>
                </div>
            `;

            // إضافة حدث النقر للانتقال إلى صفحة تفاصيل الموظف
            employeeElement.addEventListener('click', function() {
                window.location.href = `employee-details.html?id=${employee.id}`;
            });

            employeesContainer.appendChild(employeeElement);
        });
    }

    // تحديث رابط "عرض الكل"
    const viewAllLink = document.querySelector('.dashboard-section:nth-child(2) .view-all');
    if (viewAllLink) {
        viewAllLink.href = 'employees-list.html';
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    return date.toLocaleDateString('ar-IQ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// تهيئة نموذج البحث
function initSearchForm() {
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchInput = this.querySelector('.search-input');
            if (searchInput) {
                const searchTerm = searchInput.value.trim();
                if (searchTerm) {
                    // هنا يمكن إضافة منطق البحث الفعلي
                    alert(`جاري البحث عن: ${searchTerm}`);
                    searchInput.value = '';
                }
            }
        });
    }
}

// إضافة CSS للتأثيرات الديناميكية
(function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* تأثير التموج للأزرار */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* الوضع المظلم */
        .dark-mode {
            --background-color: #121212;
            --card-color: #1e1e1e;
            --text-color: #e0e0e0;
            --text-light: #a0a0a0;
            --primary-color: #3b82f6;
            --primary-dark: #2563eb;
        }

        .dark-mode .feature-card::before {
            background-color: rgba(0, 0, 0, 0.7);
        }

        .dark-mode .alert,
        .dark-mode .employee-card:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .dark-mode .employee-card {
            border-color: #333;
        }

        .dark-mode .dashboard-section h2 {
            border-color: #333;
        }

        /* تحريك العناصر عند التمرير */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero, .stats, .features, .dashboard {
            animation: fadeInUp 0.8s ease-out;
        }

        .stat-card {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }
        .stat-card:nth-child(5) { animation-delay: 0.5s; }
        .stat-card:nth-child(6) { animation-delay: 0.6s; }

        .feature-card {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .feature-card:nth-child(1) { animation-delay: 0.3s; }
        .feature-card:nth-child(2) { animation-delay: 0.5s; }
        .feature-card:nth-child(3) { animation-delay: 0.7s; }
    `;
    document.head.appendChild(style);
})();
