// الكود الخاص بصفحة العقوبات

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initPenalties();
    
    // إضافة مستمعي الأحداث
    setupEventListeners();
    
    // تحميل بيانات الموظفين
    loadEmployees();
    
    // تحميل بيانات العقوبات
    loadPenalties();
});

// تهيئة نظام العقوبات
function initPenalties() {
    // التحقق من وجود بيانات العقوبات في التخزين المحلي
    if (!localStorage.getItem('penalties')) {
        localStorage.setItem('penalties', JSON.stringify([]));
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج إضافة عقوبة جديدة
    const addPenaltyForm = document.getElementById('addPenaltyForm');
    if (addPenaltyForm) {
        addPenaltyForm.addEventListener('submit', handleAddPenalty);
    }
    
    // البحث في العقوبات
    const searchPenalty = document.getElementById('searchPenalty');
    if (searchPenalty) {
        searchPenalty.addEventListener('input', handleSearchPenalties);
    }
    
    // تصفية العقوبات حسب النوع
    const filterPenaltyType = document.getElementById('filterPenaltyType');
    if (filterPenaltyType) {
        filterPenaltyType.addEventListener('change', handleFilterPenalties);
    }
    
    // أزرار النوافذ المنبثقة
    setupModalButtons();
}

// إعداد أزرار النوافذ المنبثقة
function setupModalButtons() {
    // أزرار إغلاق النوافذ المنبثقة
    const closeButtons = document.querySelectorAll('.close-modal');
    closeButtons.forEach(button => {
        button.addEventListener('click', closeAllModals);
    });
    
    // زر إغلاق نافذة عرض التفاصيل
    const closeViewBtn = document.getElementById('closeViewBtn');
    if (closeViewBtn) {
        closeViewBtn.addEventListener('click', closeAllModals);
    }
    
    // زر تعديل العقوبة
    const editPenaltyBtn = document.getElementById('editPenaltyBtn');
    if (editPenaltyBtn) {
        editPenaltyBtn.addEventListener('click', openEditModal);
    }
    
    // زر حفظ التعديلات
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', handleEditPenalty);
    }
    
    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', closeAllModals);
    }
    
    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', handleDeletePenalty);
    }
    
    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', closeAllModals);
    }
}

// تحميل بيانات الموظفين
function loadEmployees() {
    // الحصول على بيانات الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    
    // الحصول على قوائم الموظفين
    const employeeSelect = document.getElementById('employeeId');
    const editEmployeeSelect = document.getElementById('editEmployeeId');
    
    // مسح القوائم
    if (employeeSelect) employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
    if (editEmployeeSelect) editEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
    
    // إضافة الموظفين إلى القوائم
    employees.forEach(employee => {
        const option = `<option value="${employee.id}">${employee.fullName}</option>`;
        if (employeeSelect) employeeSelect.innerHTML += option;
        if (editEmployeeSelect) editEmployeeSelect.innerHTML += option;
    });
}

// تحميل بيانات العقوبات
function loadPenalties() {
    // الحصول على بيانات العقوبات من التخزين المحلي
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    
    // الحصول على جدول العقوبات
    const penaltiesTable = document.querySelector('#penaltiesTable tbody');
    if (!penaltiesTable) return;
    
    // مسح الجدول
    penaltiesTable.innerHTML = '';
    
    // إضافة العقوبات إلى الجدول
    if (penalties.length === 0) {
        penaltiesTable.innerHTML = '<tr><td colspan="7" class="no-data">لا توجد عقوبات مسجلة</td></tr>';
    } else {
        penalties.forEach((penalty, index) => {
            // الحصول على اسم الموظف
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const employee = employees.find(emp => emp.id === penalty.employeeId) || { fullName: 'غير معروف' };
            
            // تحديد نوع العقوبة بالعربية
            let penaltyTypeArabic = '';
            switch (penalty.penaltyType) {
                case 'severe':
                    penaltyTypeArabic = 'شديدة';
                    break;
                case 'medium':
                    penaltyTypeArabic = 'متوسطة';
                    break;
                case 'light':
                    penaltyTypeArabic = 'خفيفة';
                    break;
                default:
                    penaltyTypeArabic = 'غير معروف';
            }
            
            // تحديد تأثير العقوبة بالعربية
            let penaltyEffectArabic = '';
            switch (penalty.penaltyEffect) {
                case 'none':
                    penaltyEffectArabic = 'لا يوجد';
                    break;
                case '3months':
                    penaltyEffectArabic = 'تأخير 3 أشهر';
                    break;
                case '6months':
                    penaltyEffectArabic = 'تأخير 6 أشهر';
                    break;
                case '1year':
                    penaltyEffectArabic = 'تأخير سنة';
                    break;
                default:
                    penaltyEffectArabic = 'غير معروف';
            }
            
            // إضافة صف جديد
            const row = `
                <tr data-id="${penalty.id}">
                    <td>${index + 1}</td>
                    <td>${employee.fullName}</td>
                    <td>${penaltyTypeArabic}</td>
                    <td>${formatDate(penalty.penaltyDate)}</td>
                    <td>${penalty.penaltyIssuer}</td>
                    <td>${penaltyEffectArabic}</td>
                    <td class="action-buttons">
                        <button class="action-btn view-btn" onclick="openViewModal('${penalty.id}')"><i class="fas fa-eye"></i></button>
                        <button class="action-btn edit-btn" onclick="openEditModal('${penalty.id}')"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" onclick="openDeleteModal('${penalty.id}')"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `;
            penaltiesTable.innerHTML += row;
        });
    }
    
    // تحديث معلومات الترقيم
    updatePagination(penalties.length);
}

// معالجة إضافة عقوبة جديدة
function handleAddPenalty(event) {
    event.preventDefault();
    
    // الحصول على بيانات النموذج
    const employeeId = document.getElementById('employeeId').value;
    const penaltyType = document.getElementById('penaltyType').value;
    const penaltyDate = document.getElementById('penaltyDate').value;
    const penaltyNumber = document.getElementById('penaltyNumber').value;
    const penaltyIssuer = document.getElementById('penaltyIssuer').value;
    const penaltyReason = document.getElementById('penaltyReason').value;
    const penaltyEffect = document.getElementById('penaltyEffect').value;
    
    // التحقق من صحة البيانات
    if (!employeeId || !penaltyType || !penaltyDate || !penaltyIssuer) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // إنشاء كائن العقوبة
    const penalty = {
        id: generateId(),
        employeeId,
        penaltyType,
        penaltyDate,
        penaltyNumber,
        penaltyIssuer,
        penaltyReason,
        penaltyEffect
    };
    
    // إضافة العقوبة إلى التخزين المحلي
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    penalties.push(penalty);
    localStorage.setItem('penalties', JSON.stringify(penalties));
    
    // إعادة تحميل العقوبات
    loadPenalties();
    
    // إعادة تعيين النموذج
    document.getElementById('addPenaltyForm').reset();
    
    // عرض رسالة نجاح
    showNotification('تمت إضافة العقوبة بنجاح', 'success');
}

// فتح نافذة عرض تفاصيل العقوبة
function openViewModal(penaltyId) {
    // الحصول على بيانات العقوبة
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    const penalty = penalties.find(p => p.id === penaltyId);
    
    if (!penalty) {
        showNotification('لم يتم العثور على العقوبة', 'error');
        return;
    }
    
    // الحصول على اسم الموظف
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    const employee = employees.find(emp => emp.id === penalty.employeeId) || { fullName: 'غير معروف' };
    
    // تحديد نوع العقوبة بالعربية
    let penaltyTypeArabic = '';
    switch (penalty.penaltyType) {
        case 'severe':
            penaltyTypeArabic = 'شديدة';
            break;
        case 'medium':
            penaltyTypeArabic = 'متوسطة';
            break;
        case 'light':
            penaltyTypeArabic = 'خفيفة';
            break;
        default:
            penaltyTypeArabic = 'غير معروف';
    }
    
    // تحديد تأثير العقوبة بالعربية
    let penaltyEffectArabic = '';
    switch (penalty.penaltyEffect) {
        case 'none':
            penaltyEffectArabic = 'لا يوجد';
            break;
        case '3months':
            penaltyEffectArabic = 'تأخير 3 أشهر';
            break;
        case '6months':
            penaltyEffectArabic = 'تأخير 6 أشهر';
            break;
        case '1year':
            penaltyEffectArabic = 'تأخير سنة';
            break;
        default:
            penaltyEffectArabic = 'غير معروف';
    }
    
    // ملء بيانات النافذة
    document.getElementById('viewEmployeeName').textContent = employee.fullName;
    document.getElementById('viewPenaltyType').textContent = penaltyTypeArabic;
    document.getElementById('viewPenaltyDate').textContent = formatDate(penalty.penaltyDate);
    document.getElementById('viewPenaltyNumber').textContent = penalty.penaltyNumber || 'غير محدد';
    document.getElementById('viewPenaltyIssuer').textContent = penalty.penaltyIssuer;
    document.getElementById('viewPenaltyReason').textContent = penalty.penaltyReason || 'غير محدد';
    document.getElementById('viewPenaltyEffect').textContent = penaltyEffectArabic;
    
    // تخزين معرف العقوبة للاستخدام لاحقاً
    document.getElementById('editPenaltyBtn').setAttribute('data-id', penaltyId);
    
    // فتح النافذة
    document.getElementById('viewPenaltyModal').style.display = 'block';
}

// فتح نافذة تعديل العقوبة
function openEditModal(penaltyId) {
    // إذا تم استدعاء الدالة من زر التعديل في نافذة العرض
    if (typeof penaltyId === 'object') {
        penaltyId = penaltyId.target.getAttribute('data-id');
        // إغلاق نافذة العرض
        document.getElementById('viewPenaltyModal').style.display = 'none';
    }
    
    // الحصول على بيانات العقوبة
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    const penalty = penalties.find(p => p.id === penaltyId);
    
    if (!penalty) {
        showNotification('لم يتم العثور على العقوبة', 'error');
        return;
    }
    
    // ملء نموذج التعديل
    document.getElementById('editPenaltyId').value = penalty.id;
    document.getElementById('editEmployeeId').value = penalty.employeeId;
    document.getElementById('editPenaltyType').value = penalty.penaltyType;
    document.getElementById('editPenaltyDate').value = penalty.penaltyDate;
    document.getElementById('editPenaltyNumber').value = penalty.penaltyNumber || '';
    document.getElementById('editPenaltyIssuer').value = penalty.penaltyIssuer;
    document.getElementById('editPenaltyReason').value = penalty.penaltyReason || '';
    document.getElementById('editPenaltyEffect').value = penalty.penaltyEffect;
    
    // فتح النافذة
    document.getElementById('editModal').style.display = 'block';
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(penaltyId) {
    // تخزين معرف العقوبة للاستخدام لاحقاً
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', penaltyId);
    
    // فتح النافذة
    document.getElementById('deleteModal').style.display = 'block';
}

// معالجة تعديل العقوبة
function handleEditPenalty() {
    // الحصول على بيانات النموذج
    const penaltyId = document.getElementById('editPenaltyId').value;
    const employeeId = document.getElementById('editEmployeeId').value;
    const penaltyType = document.getElementById('editPenaltyType').value;
    const penaltyDate = document.getElementById('editPenaltyDate').value;
    const penaltyNumber = document.getElementById('editPenaltyNumber').value;
    const penaltyIssuer = document.getElementById('editPenaltyIssuer').value;
    const penaltyReason = document.getElementById('editPenaltyReason').value;
    const penaltyEffect = document.getElementById('editPenaltyEffect').value;
    
    // التحقق من صحة البيانات
    if (!employeeId || !penaltyType || !penaltyDate || !penaltyIssuer) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // تحديث العقوبة في التخزين المحلي
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    const penaltyIndex = penalties.findIndex(p => p.id === penaltyId);
    
    if (penaltyIndex === -1) {
        showNotification('لم يتم العثور على العقوبة', 'error');
        return;
    }
    
    penalties[penaltyIndex] = {
        id: penaltyId,
        employeeId,
        penaltyType,
        penaltyDate,
        penaltyNumber,
        penaltyIssuer,
        penaltyReason,
        penaltyEffect
    };
    
    localStorage.setItem('penalties', JSON.stringify(penalties));
    
    // إعادة تحميل العقوبات
    loadPenalties();
    
    // إغلاق النافذة
    closeAllModals();
    
    // عرض رسالة نجاح
    showNotification('تم تحديث العقوبة بنجاح', 'success');
}

// معالجة حذف العقوبة
function handleDeletePenalty() {
    // الحصول على معرف العقوبة
    const penaltyId = document.getElementById('confirmDeleteBtn').getAttribute('data-id');
    
    // حذف العقوبة من التخزين المحلي
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    const updatedPenalties = penalties.filter(p => p.id !== penaltyId);
    
    localStorage.setItem('penalties', JSON.stringify(updatedPenalties));
    
    // إعادة تحميل العقوبات
    loadPenalties();
    
    // إغلاق النافذة
    closeAllModals();
    
    // عرض رسالة نجاح
    showNotification('تم حذف العقوبة بنجاح', 'success');
}

// معالجة البحث في العقوبات
function handleSearchPenalties() {
    // الحصول على نص البحث
    const searchText = document.getElementById('searchPenalty').value.toLowerCase();
    
    // الحصول على نوع التصفية
    const filterType = document.getElementById('filterPenaltyType').value;
    
    // تصفية العقوبات
    filterPenalties(searchText, filterType);
}

// معالجة تصفية العقوبات حسب النوع
function handleFilterPenalties() {
    // الحصول على نص البحث
    const searchText = document.getElementById('searchPenalty').value.toLowerCase();
    
    // الحصول على نوع التصفية
    const filterType = document.getElementById('filterPenaltyType').value;
    
    // تصفية العقوبات
    filterPenalties(searchText, filterType);
}

// تصفية العقوبات
function filterPenalties(searchText, filterType) {
    // الحصول على بيانات العقوبات
    const penalties = JSON.parse(localStorage.getItem('penalties')) || [];
    
    // الحصول على بيانات الموظفين
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    
    // تصفية العقوبات
    const filteredPenalties = penalties.filter(penalty => {
        // البحث في اسم الموظف
        const employee = employees.find(emp => emp.id === penalty.employeeId) || { fullName: '' };
        const nameMatch = employee.fullName.toLowerCase().includes(searchText);
        
        // البحث في الجهة المانحة
        const issuerMatch = penalty.penaltyIssuer.toLowerCase().includes(searchText);
        
        // البحث في رقم الكتاب
        const numberMatch = penalty.penaltyNumber ? penalty.penaltyNumber.toLowerCase().includes(searchText) : false;
        
        // تطبيق تصفية النوع
        const typeMatch = filterType ? penalty.penaltyType === filterType : true;
        
        return (nameMatch || issuerMatch || numberMatch) && typeMatch;
    });
    
    // عرض العقوبات المصفاة
    displayFilteredPenalties(filteredPenalties);
}

// عرض العقوبات المصفاة
function displayFilteredPenalties(filteredPenalties) {
    // الحصول على جدول العقوبات
    const penaltiesTable = document.querySelector('#penaltiesTable tbody');
    if (!penaltiesTable) return;
    
    // مسح الجدول
    penaltiesTable.innerHTML = '';
    
    // إضافة العقوبات المصفاة إلى الجدول
    if (filteredPenalties.length === 0) {
        penaltiesTable.innerHTML = '<tr><td colspan="7" class="no-data">لا توجد عقوبات مطابقة</td></tr>';
    } else {
        filteredPenalties.forEach((penalty, index) => {
            // الحصول على اسم الموظف
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const employee = employees.find(emp => emp.id === penalty.employeeId) || { fullName: 'غير معروف' };
            
            // تحديد نوع العقوبة بالعربية
            let penaltyTypeArabic = '';
            switch (penalty.penaltyType) {
                case 'severe':
                    penaltyTypeArabic = 'شديدة';
                    break;
                case 'medium':
                    penaltyTypeArabic = 'متوسطة';
                    break;
                case 'light':
                    penaltyTypeArabic = 'خفيفة';
                    break;
                default:
                    penaltyTypeArabic = 'غير معروف';
            }
            
            // تحديد تأثير العقوبة بالعربية
            let penaltyEffectArabic = '';
            switch (penalty.penaltyEffect) {
                case 'none':
                    penaltyEffectArabic = 'لا يوجد';
                    break;
                case '3months':
                    penaltyEffectArabic = 'تأخير 3 أشهر';
                    break;
                case '6months':
                    penaltyEffectArabic = 'تأخير 6 أشهر';
                    break;
                case '1year':
                    penaltyEffectArabic = 'تأخير سنة';
                    break;
                default:
                    penaltyEffectArabic = 'غير معروف';
            }
            
            // إضافة صف جديد
            const row = `
                <tr data-id="${penalty.id}">
                    <td>${index + 1}</td>
                    <td>${employee.fullName}</td>
                    <td>${penaltyTypeArabic}</td>
                    <td>${formatDate(penalty.penaltyDate)}</td>
                    <td>${penalty.penaltyIssuer}</td>
                    <td>${penaltyEffectArabic}</td>
                    <td class="action-buttons">
                        <button class="action-btn view-btn" onclick="openViewModal('${penalty.id}')"><i class="fas fa-eye"></i></button>
                        <button class="action-btn edit-btn" onclick="openEditModal('${penalty.id}')"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete-btn" onclick="openDeleteModal('${penalty.id}')"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `;
            penaltiesTable.innerHTML += row;
        });
    }
    
    // تحديث معلومات الترقيم
    updatePagination(filteredPenalties.length);
}

// تحديث معلومات الترقيم
function updatePagination(totalItems) {
    document.getElementById('currentPage').textContent = '1';
    document.getElementById('totalPages').textContent = Math.ceil(totalItems / 10) || 1;
}

// إغلاق جميع النوافذ المنبثقة
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// عرض إشعار
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    // تحديد أيقونة الإشعار
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-times-circle"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            break;
        default:
            icon = '<i class="fas fa-info-circle"></i>';
    }
    
    // إضافة محتوى الإشعار
    notification.innerHTML = `
        <div class="notification-content">
            ${icon}
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // إغلاق الإشعار تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// إنشاء معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
