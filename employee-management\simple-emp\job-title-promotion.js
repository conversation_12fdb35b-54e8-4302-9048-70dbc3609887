/**
 * ملف لإدارة ترقية العناوين الوظيفية
 */

// تحديث العنوان الوظيفي الجديد بناءً على العنوان الوظيفي الحالي
function updateNewJobTitle() {
    console.log('تحديث العنوان الوظيفي الجديد...');

    // الحصول على العنوان الوظيفي الحالي
    const jobTitleSelect = document.getElementById('jobTitle');
    const newJobTitleElement = document.getElementById('newJobTitle');

    if (!jobTitleSelect || !newJobTitleElement) {
        console.error('عناصر العنوان الوظيفي غير موجودة');
        return;
    }

    // الحصول على قيمة العنوان الوظيفي الحالي
    const currentJobTitleId = jobTitleSelect.value;
    if (!currentJobTitleId) {
        console.log('لم يتم تحديد عنوان وظيفي حالي');
        newJobTitleElement.value = '';
        return;
    }

    // الحصول على جميع العناوين الوظيفية
    const allJobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');

    // البحث عن العنوان الوظيفي الحالي
    const currentJobTitle = allJobTitles.find(title => title.id == currentJobTitleId);
    if (!currentJobTitle) {
        console.error('لم يتم العثور على العنوان الوظيفي الحالي');
        return;
    }

    console.log('العنوان الوظيفي الحالي:', currentJobTitle);

    // استخدام العنوان الوظيفي الجديد المحدد يدوياً
    if (currentJobTitle.newJobTitle) {
        console.log('استخدام العنوان الوظيفي الجديد المحدد يدوياً:', currentJobTitle.newJobTitle);
        newJobTitleElement.value = currentJobTitle.newJobTitle;
        return true;
    }

    // إذا لم يكن هناك عنوان وظيفي جديد محدد، استخدم نفس العنوان الوظيفي
    console.log('لا يوجد عنوان وظيفي جديد محدد، استخدام نفس العنوان الوظيفي');
    newJobTitleElement.value = currentJobTitle.name;
    return false;
}

// الحصول على مسارات الترقية
function getPromotionPaths() {
    // محاولة الحصول على مسارات الترقية من التخزين المحلي
    const storedPaths = localStorage.getItem('promotionPaths');
    if (storedPaths) {
        return JSON.parse(storedPaths);
    }

    // إنشاء مسارات الترقية بناءً على العلاقات المحددة في العناوين الوظيفية
    const allJobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');

    // إنشاء مسارات الترقية
    const promotionPaths = {
        teaching: [],
        technical: [],
        administrative: []
    };

    // بناء مسارات الترقية بناءً على العلاقات المحددة في nextJobTitleId
    for (const category in promotionPaths) {
        // الحصول على العناوين الوظيفية في هذه الفئة
        const categoryTitles = allJobTitles.filter(title => title.category === category);

        // بناء مسار الترقية
        const path = [];

        // البحث عن العناوين الوظيفية التي ليس لها عنوان سابق (بداية المسار)
        const startTitles = categoryTitles.filter(title => {
            // التحقق مما إذا كان هذا العنوان الوظيفي ليس nextJobTitleId لأي عنوان آخر
            return !categoryTitles.some(otherTitle =>
                otherTitle.nextJobTitleId == title.id
            );
        });

        // إضافة العناوين الوظيفية إلى المسار بالترتيب
        for (const startTitle of startTitles) {
            let currentTitle = startTitle;
            path.push(currentTitle.id);

            // متابعة المسار حتى النهاية
            while (currentTitle.nextJobTitleId) {
                const nextTitle = categoryTitles.find(title =>
                    title.id == currentTitle.nextJobTitleId
                );

                if (nextTitle) {
                    path.push(nextTitle.id);
                    currentTitle = nextTitle;
                } else {
                    break;
                }
            }
        }

        // إضافة العناوين الوظيفية المتبقية التي لم يتم تضمينها في المسار
        const remainingTitles = categoryTitles.filter(title =>
            !path.includes(title.id)
        );

        for (const title of remainingTitles) {
            path.push(title.id);
        }

        // تعيين المسار للفئة
        promotionPaths[category] = path;
    }

    // حفظ مسارات الترقية في التخزين المحلي
    localStorage.setItem('promotionPaths', JSON.stringify(promotionPaths));

    return promotionPaths;
}

// تحديث مسارات الترقية
function updatePromotionPaths() {
    // إعادة إنشاء مسارات الترقية
    const paths = getPromotionPaths();

    // حفظ مسارات الترقية المحدثة
    localStorage.setItem('promotionPaths', JSON.stringify(paths));

    console.log('تم تحديث مسارات الترقية:', paths);
    return paths;
}

// تصدير الوظائف
window.updateNewJobTitle = updateNewJobTitle;
window.getPromotionPaths = getPromotionPaths;
window.updatePromotionPaths = updatePromotionPaths;
