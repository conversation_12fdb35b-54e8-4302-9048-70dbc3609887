// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة التشكرات
    initThanksPage();
});

// تهيئة صفحة التشكرات
function initThanksPage() {
    // طباعة رسالة تشخيصية
    console.log('بدء تهيئة صفحة التشكرات');

    // التحقق من وجود بيانات موظفين في التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    console.log('عدد الموظفين في التخزين المحلي:', employees.length);

    // تحميل بيانات الموظفين
    loadEmployees();

    // تحميل بيانات التشكرات
    loadThanks();

    // تهيئة نموذج إضافة تشكر
    initAddThanksForm();

    // تهيئة البحث والتصفية
    initSearchAndFilter();

    // تهيئة النوافذ المنبثقة
    initModals();

    // إضافة زر لتحديث تواريخ جميع الموظفين
    addUpdateAllEmployeesButton();

    // طباعة رسالة تشخيصية
    console.log('اكتملت تهيئة صفحة التشكرات');
}

// تحميل بيانات الموظفين
function loadEmployees() {
    // ملء قائمة الموظفين في النموذج
    fillEmployeeSelect();
}

// ملء قائمة الموظفين في النموذج
function fillEmployeeSelect() {
    // الحصول على الموظفين من التخزين المحلي
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    console.log('fillEmployeeSelect: عدد الموظفين:', employees.length);

    // طباعة بيانات الموظفين للتشخيص
    if (employees.length > 0) {
        console.log('بيانات أول موظف:', employees[0]);
    } else {
        console.log('لا يوجد موظفين في التخزين المحلي');
    }

    // ملء قائمة الموظفين في النموذج
    const employeeSelect = document.getElementById('employeeId');

    if (employeeSelect) {
        console.log('تم العثور على عنصر employeeId');

        // الاحتفاظ بالخيار الأول (الافتراضي)
        let defaultOption;
        if (employeeSelect.options.length > 0) {
            defaultOption = employeeSelect.options[0];
        } else {
            defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'اختر الموظف';
        }

        // مسح القائمة وإضافة الخيار الافتراضي
        employeeSelect.innerHTML = '';
        employeeSelect.appendChild(defaultOption);

        // إضافة الموظفين إلى القائمة
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} (${employee.id})`;
            employeeSelect.appendChild(option);
        });

        console.log('تم ملء قائمة الموظفين بـ', employees.length, 'موظف');
    } else {
        console.log('لم يتم العثور على عنصر employeeId');
    }

    // ملء قائمة الموظفين في نموذج التعديل
    const editEmployeeSelect = document.getElementById('editEmployeeId');

    if (editEmployeeSelect) {
        console.log('تم العثور على عنصر editEmployeeId');

        // الاحتفاظ بالخيار الأول (الافتراضي)
        let defaultOption;
        if (editEmployeeSelect.options.length > 0) {
            defaultOption = editEmployeeSelect.options[0];
        } else {
            defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = 'اختر الموظف';
        }

        // مسح القائمة وإضافة الخيار الافتراضي
        editEmployeeSelect.innerHTML = '';
        editEmployeeSelect.appendChild(defaultOption);

        // إضافة الموظفين إلى القائمة
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} (${employee.id})`;
            editEmployeeSelect.appendChild(option);
        });

        console.log('تم ملء قائمة الموظفين في نموذج التعديل بـ', employees.length, 'موظف');
    } else {
        console.log('لم يتم العثور على عنصر editEmployeeId');
    }
}

// تحميل بيانات التشكرات
function loadThanks() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let thanks = localStorage.getItem('thanks');

    if (!thanks) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        thanks = [
            {
                id: 1,
                employeeId: 1001,
                type: 'ministerial',
                date: '2023-05-15',
                number: 'M/2023/123',
                issuer: 'وزارة التعليم العالي',
                reason: 'للجهود المتميزة في تطوير المناهج الدراسية',
                effect: '1month_minister'
            },
            {
                id: 2,
                employeeId: 1002,
                type: 'university',
                date: '2023-06-20',
                number: 'U/2023/456',
                issuer: 'رئاسة الجامعة',
                reason: 'للمشاركة الفعالة في المؤتمر العلمي الدولي',
                effect: '1month_university'
            },
            {
                id: 3,
                employeeId: 1003,
                type: 'presidential',
                date: '2023-07-10',
                number: 'P/2023/789',
                issuer: 'رئاسة مجلس الوزراء',
                reason: 'للإنجازات المتميزة في مجال البحث العلمي',
                effect: '6months_pm'
            },
            {
                id: 4,
                employeeId: 1004,
                type: 'college',
                date: '2023-08-05',
                number: 'C/2023/321',
                issuer: 'عمادة كلية الهندسة',
                reason: 'للتميز في التدريس والإرشاد الأكاديمي',
                effect: 'none'
            }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        thanks = JSON.parse(thanks);
    }

    // عرض البيانات في الجدول
    displayThanks(thanks);
}

// عرض التشكرات في الجدول
function displayThanks(thanks) {
    const tableBody = document.querySelector('#thanksTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك تشكرات، عرض رسالة
    if (thanks.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="7" class="text-center">لا توجد تشكرات. أضف تشكر جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // ترتيب التشكرات حسب التاريخ (الأحدث أولاً)
    thanks.sort((a, b) => new Date(b.date) - new Date(a.date));

    // تنظيم التشكرات حسب الموظف
    const thanksByEmployee = {};
    thanks.forEach(thank => {
        if (!thanksByEmployee[thank.employeeId]) {
            thanksByEmployee[thank.employeeId] = [];
        }
        thanksByEmployee[thank.employeeId].push(thank);
    });

    // إضافة الصفوف إلى الجدول
    let rowIndex = 1;

    // التحقق من وجود موظفين غير موجودين في قاعدة البيانات
    const missingEmployees = [];
    Object.keys(thanksByEmployee).forEach(employeeId => {
        const employee = employees.find(emp => emp.id == employeeId);
        if (!employee) {
            missingEmployees.push(employeeId);
        }
    });

    // إذا كان هناك موظفين غير موجودين، عرض تنبيه
    if (missingEmployees.length > 0) {
        const warningRow = document.createElement('tr');
        warningRow.innerHTML = `
            <td colspan="7">
                <div class="thanks-alert warning">
                    <h3><i class="fas fa-exclamation-triangle"></i> تنبيه</h3>
                    <p>تم العثور على كتب شكر لموظفين غير موجودين في قاعدة البيانات. يرجى التحقق من بيانات الموظفين.</p>
                </div>
            </td>
        `;
        tableBody.appendChild(warningRow);
    }

    // إضافة صفوف للتشكرات مرتبة حسب الموظف
    Object.keys(thanksByEmployee).forEach(employeeId => {
        const employeeThanks = thanksByEmployee[employeeId];
        const employee = employees.find(emp => emp.id == employeeId);
        const employeeName = employee ? employee.name : 'موظف غير موجود (ID: ' + employeeId + ')';

        // إضافة صف رأسي للموظف
        const headerRow = document.createElement('tr');
        headerRow.className = 'employee-header';
        headerRow.style.backgroundColor = '#f8f9fa';

        // الحصول على الأحرف الأولى من اسم الموظف
        let avatarContent;
        let headerStyle = '';

        if (employee) {
            const nameParts = employeeName.split(' ');
            avatarContent = nameParts.length > 1
                ? `${nameParts[0].charAt(0)}${nameParts[1].charAt(0)}`
                : nameParts[0].charAt(0);
        } else {
            // إذا كان الموظف غير موجود، استخدم أيقونة تحذير
            avatarContent = '<i class="fas fa-exclamation-triangle"></i>';
            headerStyle = 'background-color: #fff3cd; color: #856404;';

        headerRow.innerHTML = `
            <td colspan="7" style="${headerStyle}">
                <div class="employee-name">
                    <div class="employee-avatar" style="${!employee ? 'background-color: #ffc107;' : ''}">${avatarContent}</div>
                    <strong>${employeeName}</strong>
                    <span class="badge" style="margin-right: 10px; background-color: ${employee ? '#007bff' : '#ffc107'}; color: ${employee ? 'white' : '#856404'}; padding: 3px 8px; border-radius: 10px;">
                        ${employeeThanks.length} كتاب شكر
                    </span>
                    ${!employee ? '<button class="btn btn-sm btn-warning fix-employee-btn" data-employee-id="' + employeeId + '" style="margin-right: 10px; font-size: 12px;">إصلاح</button>' : ''}
                </div>
            </td>
        `;
        tableBody.appendChild(headerRow);

        // إضافة صفوف لكتب شكر الموظف
        employeeThanks.forEach((thank, index) => {
            const row = document.createElement('tr');

            // تحويل نوع التشكر إلى نص عربي
            let typeText = '';
            let typeClass = '';
            switch (thank.type) {
                case 'ministerial':
                    typeText = 'وزاري';
                    typeClass = 'thanks-ministerial';
                    break;
                case 'presidential':
                    typeText = 'رئاسي';
                    typeClass = 'thanks-presidential';
                    break;
                case 'university':
                    typeText = 'جامعي';
                    typeClass = 'thanks-university';
                    break;
                default:
                    typeText = thank.type;
            }

            // تحويل تأثير التشكر إلى نص عربي
            let effectText = '';
            let effectClass = '';
            switch (thank.effect) {
                case 'none':
                    effectText = 'لا يوجد';
                    effectClass = 'none';
                    break;
                case '1month_university':
                    effectText = 'رئيس الجامعة - تقديم شهر واحد';
                    effectClass = 'university';
                    break;
                case '1month_minister':
                    effectText = 'وزير التعليم العالي - تقديم شهر واحد';
                    effectClass = 'minister';
                    break;
                case '6months_pm':
                    effectText = 'رئيس الوزراء - تقديم 6 أشهر';
                    effectClass = 'pm';
                    break;
                // الحفاظ على التوافق مع البيانات القديمة
                case '3months':
                    effectText = 'تقديم 3 أشهر';
                    effectClass = 'minister';
                    break;
                case '6months':
                    effectText = 'تقديم 6 أشهر';
                    effectClass = 'pm';
                    break;
                case '1year':
                    effectText = 'تقديم سنة';
                    effectClass = 'pm';
                    break;
                default:
                    effectText = thank.effect;
            }

            row.innerHTML = `
                <td>${rowIndex++}</td>
                <td>-</td>
                <td><span class="thanks-type ${typeClass}">${typeText}</span></td>
                <td><span class="thanks-date"><i class="far fa-calendar-alt"></i> ${formatDate(thank.date)}</span></td>
                <td>${thank.issuer}</td>
                <td><span class="thanks-effect ${effectClass}">${effectText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-btn" data-id="${thank.id}" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-btn" data-id="${thank.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${thank.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tableBody.appendChild(row);
        });
    });

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار الإجراءات
function addActionButtonsEventListeners() {
    // أزرار العرض
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const thankId = this.getAttribute('data-id');
            openViewModal(thankId);
        });
    });

    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const thankId = this.getAttribute('data-id');
            openEditModal(thankId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const thankId = this.getAttribute('data-id');
            openDeleteModal(thankId);
        });
    });

    // أزرار إصلاح الموظف
    const fixEmployeeButtons = document.querySelectorAll('.fix-employee-btn');
    fixEmployeeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-employee-id');
            fixMissingEmployee(employeeId);
        });
    });
}

// تهيئة نموذج إضافة تشكر
function initAddThanksForm() {
    const addForm = document.getElementById('addThanksForm');
    if (!addForm) return;

    // تهيئة قوائم الموظفين
    initEmployeeSelections();

    // ملء قائمة الموظفين في النموذج
    fillEmployeeSelect();

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على نوع اختيار الموظفين
        const employeeSelection = document.getElementById('employeeSelection').value;

        // الحصول على قيم الحقول المشتركة
        const type = document.getElementById('thanksType').value;
        const date = document.getElementById('thanksDate').value;
        const number = document.getElementById('thanksNumber').value;
        const issuer = document.getElementById('thanksIssuer').value;
        const reason = document.getElementById('thanksReason').value;
        const effect = document.getElementById('thanksEffect').value;

        // التحقق من صحة البيانات المشتركة
        if (!type) {
            alert('يرجى اختيار نوع التشكر');
            return;
        }

        if (!date) {
            alert('يرجى إدخال تاريخ التشكر');
            return;
        }

        if (!issuer) {
            alert('يرجى إدخال الجهة المانحة');
            return;
        }

        // إضافة التشكر بناءً على نوع اختيار الموظفين
        if (employeeSelection === 'single') {
            // التحقق من اختيار موظف
            const employeeId = document.getElementById('employeeId').value;
            if (!employeeId) {
                alert('يرجى اختيار الموظف');
                return;
            }

            // إضافة التشكر لموظف واحد
            addThank(employeeId, type, date, number, issuer, reason, effect);

            // عرض رسالة نجاح
            showNotification('تمت إضافة التشكر بنجاح', 'success');
        } else if (employeeSelection === 'multiple') {
            // التحقق من اختيار موظفين
            const multipleEmployees = document.getElementById('multipleEmployees');
            const selectedEmployees = Array.from(multipleEmployees.selectedOptions).map(option => option.value);

            if (selectedEmployees.length === 0) {
                alert('يرجى اختيار موظف واحد على الأقل');
                return;
            }

            // إضافة تشكر لكل موظف مختار
            let addedCount = 0;
            selectedEmployees.forEach(employeeId => {
                addThank(employeeId, type, date, number, issuer, reason, effect);
                addedCount++;
            });

            // عرض رسالة نجاح
            showNotification(`تمت إضافة ${addedCount} تشكر بنجاح`, 'success');
        } else if (employeeSelection === 'all') {
            // الحصول على جميع الموظفين
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const departmentFilter = document.getElementById('departmentFilter').value;

            // تصفية الموظفين حسب القسم إذا تم اختيار قسم
            let filteredEmployees = employees;
            if (departmentFilter) {
                filteredEmployees = employees.filter(employee => employee.department === departmentFilter);
            }

            if (filteredEmployees.length === 0) {
                alert('لا يوجد موظفين متاحين');
                return;
            }

            // إضافة تشكر لكل موظف
            let addedCount = 0;
            filteredEmployees.forEach(employee => {
                addThank(employee.id, type, date, number, issuer, reason, effect);
                addedCount++;
            });

            // عرض رسالة نجاح
            showNotification(`تمت إضافة ${addedCount} تشكر بنجاح`, 'success');
        }

        // إعادة تعيين النموذج
        this.reset();

        // إعادة عرض حاوية الموظف الواحد
        document.getElementById('singleEmployeeContainer').style.display = 'block';
        document.getElementById('multipleEmployeeContainer').style.display = 'none';
        document.getElementById('departmentFilterContainer').style.display = 'none';
    });
}

// تهيئة قوائم اختيار الموظفين
function initEmployeeSelections() {
    const employeeSelectionSelect = document.getElementById('employeeSelection');
    const multipleEmployeesSelect = document.getElementById('multipleEmployees');
    const departmentFilterSelect = document.getElementById('departmentFilter');

    // ملء قائمة الموظفين المتعددة
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    if (multipleEmployeesSelect) {
        // مسح القائمة أولاً
        multipleEmployeesSelect.innerHTML = '';

        // إضافة الموظفين إلى القائمة
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} (${employee.id})`;
            multipleEmployeesSelect.appendChild(option);
        });
    }

    // ملء قائمة الأقسام
    if (departmentFilterSelect) {
        // الحصول على قائمة فريدة من الأقسام
        const departments = [...new Set(employees.map(employee => employee.department).filter(Boolean))];

        // الاحتفاظ بالخيار الأول (الافتراضي)
        const defaultOption = departmentFilterSelect.options[0];
        departmentFilterSelect.innerHTML = '';
        departmentFilterSelect.appendChild(defaultOption);

        // إضافة الأقسام إلى القائمة
        departments.forEach(department => {
            const option = document.createElement('option');
            option.value = department;
            option.textContent = department;
            departmentFilterSelect.appendChild(option);
        });
    }

    // إضافة مستمع حدث لتغيير نوع اختيار الموظفين
    if (employeeSelectionSelect) {
        employeeSelectionSelect.addEventListener('change', function() {
            const selectedValue = this.value;
            const singleEmployeeContainer = document.getElementById('singleEmployeeContainer');
            const multipleEmployeeContainer = document.getElementById('multipleEmployeeContainer');
            const departmentFilterContainer = document.getElementById('departmentFilterContainer');

            // إخفاء جميع الحاويات أولاً
            singleEmployeeContainer.style.display = 'none';
            multipleEmployeeContainer.style.display = 'none';
            departmentFilterContainer.style.display = 'none';

            // إظهار الحاوية المناسبة بناءً على الاختيار
            if (selectedValue === 'single') {
                singleEmployeeContainer.style.display = 'block';
            } else if (selectedValue === 'multiple') {
                multipleEmployeeContainer.style.display = 'block';
                departmentFilterContainer.style.display = 'block';
            } else if (selectedValue === 'all') {
                departmentFilterContainer.style.display = 'block';
            }
        });
    }

    // إضافة مستمع حدث لتصفية الموظفين حسب القسم
    if (departmentFilterSelect) {
        departmentFilterSelect.addEventListener('change', function() {
            const selectedDepartment = this.value;
            filterEmployeesByDepartment(multipleEmployeesSelect, selectedDepartment);
        });
    }
}

// تصفية الموظفين حسب القسم
function filterEmployeesByDepartment(employeeSelect, department) {
    if (!employeeSelect) return;

    // الحصول على جميع الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // تصفية الموظفين حسب القسم
    let filteredEmployees = employees;
    if (department) {
        filteredEmployees = employees.filter(employee => employee.department === department);
    }

    // تحديث قائمة الموظفين
    employeeSelect.innerHTML = '';
    filteredEmployees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;
        option.textContent = `${employee.name} (${employee.id})`;
        employeeSelect.appendChild(option);
    });
}

// إضافة تشكر جديد
function addThank(employeeId, type, date, number, issuer, reason, effect) {
    // الحصول على التشكرات الحالية
    let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

    // إنشاء معرف فريد جديد
    const newId = thanks.length > 0 ? Math.max(...thanks.map(thank => thank.id)) + 1 : 1;

    // إنشاء التشكر الجديد
    const newThank = {
        id: newId,
        employeeId: parseInt(employeeId),
        type: type,
        date: date,
        number: number,
        issuer: issuer,
        reason: reason,
        effect: effect
    };

    // إضافة التشكر الجديد إلى المصفوفة
    thanks.push(newThank);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('thanks', JSON.stringify(thanks));

    // تحديث عرض الجدول
    displayThanks(thanks);

    // تحديث تواريخ الاستحقاق والترفيع للموظف بناءً على كتب الشكر
    let updated = false;

    // استخدام الخوارزمية الجديدة لاحتساب تأثير كتب الشكر
    if (window.thanksCalculator) {
        // تحديث تاريخ الاستحقاق الجديد
        const allowanceUpdated = window.thanksCalculator.updateNewDueDateBasedOnThanks(employeeId);

        // تحديث تاريخ الترفيع القادم
        const promotionUpdated = window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employeeId);

        updated = allowanceUpdated || promotionUpdated;
    } else {
        // استخدام الطريقة القديمة كاحتياط
        updated = updateEmployeeAllowanceDate(employeeId, date, effect);
    }

    // عرض رسالة نجاح
    if (updated) {
        showNotification(`تم إضافة التشكر وتحديث تواريخ الاستحقاق والترفيع بنجاح`, 'success');
    } else {
        showNotification('تم إضافة التشكر بنجاح', 'success');
    }

    return updated;
}

// تحديث تاريخ العلاوة للموظف بناءً على التشكر
function updateEmployeeAllowanceDate(employeeId, thankDate, effect) {
    // إذا كان التشكر لا يؤثر على العلاوة
    if (effect === 'none') return;

    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employeeIndex = employees.findIndex(emp => emp.id == employeeId);

    if (employeeIndex === -1) {
        showNotification(`لم يتم العثور على الموظف برقم ${employeeId}`, 'error');
        return;
    }

    const employee = employees[employeeIndex];

    // إصلاح تلقائي لتواريخ الاستحقاق إذا كانت غير موجودة
    let updated = false;

    // إذا كان تاريخ التعيين موجودًا ولكن تاريخ الاستحقاق الحالي غير موجود
    if (employee.hireDate && !employee.currentDueDate) {
        employee.currentDueDate = employee.hireDate;
        updated = true;
        console.log(`تم إصلاح تاريخ الاستحقاق الحالي للموظف ${employee.name} تلقائيًا`);
    }

    // إذا كان تاريخ الاستحقاق الحالي موجودًا ولكن تاريخ الاستحقاق الجديد غير موجود
    if (employee.currentDueDate && !employee.newDueDate) {
        const currentDueDate = new Date(employee.currentDueDate);
        const newDueDate = new Date(currentDueDate);
        newDueDate.setFullYear(newDueDate.getFullYear() + 1);
        employee.newDueDate = newDueDate.toISOString().split('T')[0];
        updated = true;
        console.log(`تم إصلاح تاريخ الاستحقاق الجديد للموظف ${employee.name} تلقائيًا`);
    }

    // حفظ التغييرات إذا تم التحديث
    if (updated) {
        localStorage.setItem('employees', JSON.stringify(employees));
        showNotification(`تم إصلاح تواريخ الاستحقاق للموظف ${employee.name} تلقائيًا`, 'success');
    }

    // التحقق مرة أخرى من وجود تاريخ الاستحقاق الحالي وتاريخ الاستحقاق الجديد
    if (!employee.currentDueDate || !employee.newDueDate) {
        console.log(`الموظف ${employee.name} ليس لديه تاريخ استحقاق حالي أو جديد`);
        console.log('بيانات الموظف:', employee);
        showNotification(`لا يمكن تطبيق كتاب الشكر: الموظف ${employee.name} ليس لديه تاريخ استحقاق كامل`, 'error');
        return;
    }

    // تحويل التواريخ إلى كائنات Date
    const currentDueDate = new Date(employee.currentDueDate);
    const newDueDate = new Date(employee.newDueDate);
    const thankDateObj = new Date(thankDate);

    // تحديد عدد الأشهر للتقديم بناءً على نوع التأثير
    let monthsToAdvance = 0;
    if (effect === '1month_university' || effect === '1month_minister') {
        monthsToAdvance = 1;
    } else if (effect === '6months_pm') {
        monthsToAdvance = 6;
    }

    // طباعة التواريخ للتشخيص
    console.log('تاريخ الاستحقاق الحالي:', currentDueDate.toISOString().split('T')[0]);
    console.log('تاريخ الاستحقاق الجديد:', newDueDate.toISOString().split('T')[0]);
    console.log('تاريخ كتاب الشكر:', thankDateObj.toISOString().split('T')[0]);

    // تطبيق تأثير كتاب الشكر بغض النظر عن التواريخ (تجاوز للشرط السابق)
    console.log('تطبيق تأثير كتاب الشكر بغض النظر عن التواريخ');

    // حساب التاريخ الجديد للعلاوة القادمة (تقديم بعدد الأشهر المحدد)
    const advancedDueDate = new Date(newDueDate);
    advancedDueDate.setMonth(advancedDueDate.getMonth() - monthsToAdvance);

    // تحديث تاريخ الاستحقاق الجديد للموظف
    employees[employeeIndex].newDueDate = advancedDueDate.toISOString().split('T')[0];

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('employees', JSON.stringify(employees));

    // عرض رسالة نجاح
    showNotification(`تم تقديم العلاوة للموظف ${employee.name} بمقدار ${monthsToAdvance} شهر`, 'success');

    // إضافة سجل في التخزين المحلي لتتبع التغييرات
    const allowanceChanges = JSON.parse(localStorage.getItem('allowanceChanges') || '[]');
    allowanceChanges.push({
        employeeId: employee.id,
        employeeName: employee.name,
        oldDate: newDueDate.toISOString().split('T')[0],
        newDate: advancedDueDate.toISOString().split('T')[0],
        reason: `كتاب شكر - ${getEffectText(effect)}`,
        date: new Date().toISOString().split('T')[0]
    });
    localStorage.setItem('allowanceChanges', JSON.stringify(allowanceChanges));

    return true; // تم تنفيذ التغيير بنجاح
}

// الحصول على نص تأثير التشكر
function getEffectText(effect) {
    switch (effect) {
        case 'none':
            return 'لا يوجد';
        case '1month_university':
            return 'رئيس الجامعة - تقديم شهر واحد';
        case '1month_minister':
            return 'وزير التعليم العالي - تقديم شهر واحد';
        case '6months_pm':
            return 'رئيس الوزراء - تقديم 6 أشهر';
        default:
            return effect;
    }
}

// تهيئة البحث والتصفية
function initSearchAndFilter() {
    const searchInput = document.getElementById('searchThanks');
    const filterTypeSelect = document.getElementById('filterThanksType');
    const filterEffectSelect = document.getElementById('filterThanksEffect');

    if (searchInput) {
        searchInput.addEventListener('input', applySearchAndFilter);
    }

    if (filterTypeSelect) {
        filterTypeSelect.addEventListener('change', applySearchAndFilter);
    }

    if (filterEffectSelect) {
        filterEffectSelect.addEventListener('change', applySearchAndFilter);
    }
}

// إضافة زر لتحديث تواريخ جميع الموظفين
function addUpdateAllEmployeesButton() {
    // إنشاء حاوية للزر
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'update-all-container';

    // إضافة تنبيه توضيحي
    const alertDiv = document.createElement('div');
    alertDiv.className = 'thanks-alert info';
    alertDiv.innerHTML = `
        <h3><i class="fas fa-info-circle"></i> معلومات حول كتب الشكر</h3>
        <p>يتم احتساب كتب الشكر على أساس السنة الوظيفية، حيث يحق للموظف ثلاث كتب شكر وتقدير خلال السنة الوظيفية الواحدة بما يحقق له قدماً ثلاث أشهر.</p>
        <p>كتب الشكر تؤثر على العلاوة فقط إذا كان تاريخها بعد تاريخ آخر استحقاق، بينما تؤثر جميع كتب الشكر على الترفيع بغض النظر عن تاريخها.</p>
    `;
    buttonContainer.appendChild(alertDiv);

    // إنشاء الزر
    const updateButton = document.createElement('button');
    updateButton.className = 'update-all-btn';
    updateButton.innerHTML = '<i class="fas fa-sync-alt"></i> تحديث تواريخ جميع الموظفين بناءً على كتب الشكر';

    // إضافة مستمع حدث للزر
    updateButton.addEventListener('click', function() {
        if (window.thanksCalculator) {
            // عرض رسالة تأكيد
            if (confirm('هل أنت متأكد من رغبتك في تحديث تواريخ جميع الموظفين بناءً على كتب الشكر؟')) {
                // تحديث تواريخ جميع الموظفين
                const updatedCount = window.thanksCalculator.updateAllEmployeesDatesBasedOnThanks();

                // عرض رسالة نجاح
                showNotification(`تم تحديث تواريخ ${updatedCount} موظف بنجاح`, 'success');
            }
        } else {
            showNotification('لم يتم العثور على وحدة حساب تأثير كتب الشكر', 'error');
        }
    });

    // إضافة الزر إلى الحاوية
    buttonContainer.appendChild(updateButton);

    // إضافة الحاوية إلى الصفحة
    const thanksContainer = document.querySelector('.thanks-container');
    if (thanksContainer) {
        thanksContainer.appendChild(buttonContainer);
    }
}

// تطبيق البحث والتصفية
function applySearchAndFilter() {
    const searchInput = document.getElementById('searchThanks');
    const filterTypeSelect = document.getElementById('filterThanksType');
    const filterEffectSelect = document.getElementById('filterThanksEffect');

    if (!searchInput || !filterTypeSelect || !filterEffectSelect) return;

    const searchTerm = searchInput.value.trim().toLowerCase();
    const filterType = filterTypeSelect.value;
    const filterEffect = filterEffectSelect.value;

    // الحصول على التشكرات
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // تصفية التشكرات بناءً على مصطلح البحث والنوع والتأثير
    const filteredThanks = thanks.filter(thank => {
        // البحث عن اسم الموظف
        const employee = employees.find(emp => emp.id == thank.employeeId);
        const employeeName = employee ? employee.name : '';

        const matchesSearch = employeeName.toLowerCase().includes(searchTerm) ||
                             thank.issuer.toLowerCase().includes(searchTerm) ||
                             (thank.reason && thank.reason.toLowerCase().includes(searchTerm));

        const matchesType = !filterType || thank.type === filterType;
        const matchesEffect = !filterEffect || thank.effect === filterEffect;

        return matchesSearch && matchesType && matchesEffect;
    });

    // عرض النتائج المصفاة
    displayThanks(filteredThanks);
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const viewModal = document.getElementById('viewThanksModal');
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // إعادة ملء قائمة الموظفين عند فتح نافذة التعديل
    if (editModal) {
        editModal.addEventListener('click', function(e) {
            if (e.target === editModal) {
                fillEmployeeSelect();
            }
        });
    }

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إغلاق نافذة العرض
    const closeViewBtn = document.getElementById('closeViewBtn');
    if (closeViewBtn) {
        closeViewBtn.addEventListener('click', function() {
            closeModal(viewModal);
        });
    }

    // زر التعديل في نافذة العرض
    const editThanksBtn = document.getElementById('editThanksBtn');
    if (editThanksBtn) {
        editThanksBtn.addEventListener('click', function() {
            closeModal(viewModal);
            const thankId = this.getAttribute('data-id');
            openEditModal(thankId);
        });
    }

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedThank();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const thankId = this.getAttribute('data-id');
            deleteThank(thankId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة عرض تفاصيل التشكر
function openViewModal(thankId) {
    // الحصول على بيانات التشكر
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const thank = thanks.find(t => t.id == thankId);

    if (!thank) return;

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // البحث عن اسم الموظف
    const employee = employees.find(emp => emp.id == thank.employeeId);
    const employeeName = employee ? employee.name : 'غير معروف';

    // تحويل نوع التشكر إلى نص عربي
    let typeText = '';
    switch (thank.type) {
        case 'ministerial':
            typeText = 'وزاري';
            break;
        case 'presidential':
            typeText = 'رئاسي';
            break;
        case 'university':
            typeText = 'جامعي';
            break;
        default:
            typeText = thank.type;
    }

    // تحويل تأثير التشكر إلى نص عربي
    let effectText = '';
    switch (thank.effect) {
        case 'none':
            effectText = 'لا يوجد';
            break;
        case '1month_university':
            effectText = 'رئيس الجامعة - تقديم شهر واحد';
            break;
        case '1month_minister':
            effectText = 'وزير التعليم العالي - تقديم شهر واحد';
            break;
        case '6months_pm':
            effectText = 'رئيس الوزراء - تقديم 6 أشهر';
            break;
        // الحفاظ على التوافق مع البيانات القديمة
        case '3months':
            effectText = 'تقديم 3 أشهر';
            break;
        case '6months':
            effectText = 'تقديم 6 أشهر';
            break;
        case '1year':
            effectText = 'تقديم سنة';
            break;
        default:
            effectText = thank.effect;
    }

    // ملء بيانات التشكر في النافذة
    document.getElementById('viewEmployeeName').textContent = employeeName;
    document.getElementById('viewThanksType').textContent = typeText;
    document.getElementById('viewThanksDate').textContent = formatDate(thank.date);
    document.getElementById('viewThanksNumber').textContent = thank.number || '-';
    document.getElementById('viewThanksIssuer').textContent = thank.issuer;
    document.getElementById('viewThanksReason').textContent = thank.reason || '-';
    document.getElementById('viewThanksEffect').textContent = effectText;

    // تعيين معرف التشكر لزر التعديل
    document.getElementById('editThanksBtn').setAttribute('data-id', thank.id);

    // فتح النافذة
    const viewModal = document.getElementById('viewThanksModal');
    openModal(viewModal);
}

// فتح نافذة تعديل التشكر
function openEditModal(thankId) {
    // إعادة ملء قائمة الموظفين أولاً
    fillEmployeeSelect();

    // الحصول على بيانات التشكر
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const thank = thanks.find(t => t.id == thankId);

    if (!thank) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editThanksId').value = thank.id;
    document.getElementById('editEmployeeId').value = thank.employeeId;
    document.getElementById('editThanksType').value = thank.type;
    document.getElementById('editThanksDate').value = thank.date;
    document.getElementById('editThanksNumber').value = thank.number || '';
    document.getElementById('editThanksIssuer').value = thank.issuer;
    document.getElementById('editThanksReason').value = thank.reason || '';
    document.getElementById('editThanksEffect').value = thank.effect;

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(thankId) {
    // تعيين معرف التشكر لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', thankId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ التشكر المعدل
function saveEditedThank() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editThanksId').value;
    const employeeId = document.getElementById('editEmployeeId').value;
    const type = document.getElementById('editThanksType').value;
    const date = document.getElementById('editThanksDate').value;
    const number = document.getElementById('editThanksNumber').value;
    const issuer = document.getElementById('editThanksIssuer').value;
    const reason = document.getElementById('editThanksReason').value;
    const effect = document.getElementById('editThanksEffect').value;

    // التحقق من صحة البيانات
    if (!employeeId) {
        alert('يرجى اختيار الموظف');
        return;
    }

    if (!type) {
        alert('يرجى اختيار نوع التشكر');
        return;
    }

    if (!date) {
        alert('يرجى إدخال تاريخ التشكر');
        return;
    }

    if (!issuer) {
        alert('يرجى إدخال الجهة المانحة');
        return;
    }

    // الحصول على التشكرات الحالية
    let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

    // البحث عن التشكر وتحديثه
    const index = thanks.findIndex(t => t.id == id);
    if (index !== -1) {
        // الحصول على التشكر القديم قبل التعديل
        const oldThank = thanks[index];

        // تحديث التشكر
        thanks[index] = {
            id: parseInt(id),
            employeeId: parseInt(employeeId),
            type: type,
            date: date,
            number: number,
            issuer: issuer,
            reason: reason,
            effect: effect
        };

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // تحديث عرض الجدول
        displayThanks(thanks);

        // إذا تغير التأثير أو التاريخ، قم بتحديث تاريخ العلاوة للموظف
        if (oldThank.effect !== effect || oldThank.date !== date) {
            // إذا كان التأثير القديم يؤثر على العلاوة، قم بإلغاء تأثيره أولاً
            if (oldThank.effect !== 'none') {
                resetEmployeeAllowanceDate(employeeId);
            }

            // ثم قم بتطبيق التأثير الجديد
            updateEmployeeAllowanceDate(employeeId, date, effect);
        }

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));

        // عرض رسالة نجاح
        showNotification('تم تحديث التشكر بنجاح', 'success');
    }
}

// إعادة تعيين تاريخ العلاوة للموظف
function resetEmployeeAllowanceDate(employeeId) {
    // الحصول على بيانات الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employeeIndex = employees.findIndex(emp => emp.id == employeeId);

    if (employeeIndex === -1) return;

    const employee = employees[employeeIndex];

    // التحقق من وجود تاريخ الاستحقاق الحالي
    if (!employee.currentDueDate) return;

    // حساب تاريخ الاستحقاق الجديد بناءً على تاريخ الاستحقاق الحالي (إضافة سنة)
    const currentDueDate = new Date(employee.currentDueDate);
    const newDueDate = new Date(currentDueDate);
    newDueDate.setFullYear(newDueDate.getFullYear() + 1);

    // تحديث تاريخ الاستحقاق الجديد للموظف
    employees[employeeIndex].newDueDate = newDueDate.toISOString().split('T')[0];

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('employees', JSON.stringify(employees));

    // إضافة سجل في التخزين المحلي لتتبع التغييرات
    const allowanceChanges = JSON.parse(localStorage.getItem('allowanceChanges') || '[]');
    allowanceChanges.push({
        employeeId: employee.id,
        employeeName: employee.name,
        oldDate: employee.newDueDate,
        newDate: newDueDate.toISOString().split('T')[0],
        reason: 'إعادة تعيين تاريخ الاستحقاق',
        date: new Date().toISOString().split('T')[0]
    });
    localStorage.setItem('allowanceChanges', JSON.stringify(allowanceChanges));

    // عرض رسالة نجاح
    showNotification(`تم إعادة تعيين تاريخ الاستحقاق للموظف ${employee.name}`, 'success');

    return true; // تم تنفيذ التغيير بنجاح
}

// حذف التشكر
function deleteThank(thankId) {
    // الحصول على التشكرات الحالية
    let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

    // البحث عن التشكر قبل حذفه
    const thank = thanks.find(t => t.id == thankId);

    if (!thank) return;

    // حفظ معرف الموظف وتأثير التشكر
    const employeeId = thank.employeeId;
    const effect = thank.effect;

    // حذف التشكر
    thanks = thanks.filter(t => t.id != thankId);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('thanks', JSON.stringify(thanks));

    // تحديث عرض الجدول
    displayThanks(thanks);

    // إذا كان التشكر يؤثر على العلاوة، قم بإعادة تعيين تاريخ العلاوة للموظف
    if (effect !== 'none') {
        resetEmployeeAllowanceDate(employeeId);
    }

    // عرض رسالة نجاح
    showNotification('تم حذف التشكر بنجاح', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    return date.toLocaleDateString('ar-IQ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// عرض إشعار - تم نقل هذه الوظيفة إلى ملف simple-messages.js
