// تحميل تقرير العلاوات المستحقة المطور
document.addEventListener('DOMContentLoaded', function() {
    loadSalaryScale();
    loadAdvancedAllowanceReport();
    setupAdvancedEventListeners();
});

// تحميل سلم الرواتب
let salaryScaleData = null;

function loadSalaryScale() {
    const savedScale = localStorage.getItem('salaryScale');
    if (savedScale) {
        salaryScaleData = JSON.parse(savedScale);
    } else {
        // سلم الرواتب الافتراضي حسب القانون العراقي رقم 22 لسنة 2008
        salaryScaleData = {
            lastUpdate: '2025/01/01',
            grades: [
                { grade: 1, annualAllowance: 20000, promotionYears: 5, salaries: [910000, 930000, 950000, 970000, 990000, 1010000, 1030000, 1050000, 1070000, 1090000, 1110000] },
                { grade: 2, annualAllowance: 17000, promotionYears: 5, salaries: [773000, 790000, 807000, 824000, 841000, 858000, 875000, 892000, 909000, 926000, 943000] },
                { grade: 3, annualAllowance: 15000, promotionYears: 5, salaries: [600000, 610000, 620000, 630000, 640000, 650000, 660000, 670000, 680000, 690000, 700000] },
                { grade: 4, annualAllowance: 12000, promotionYears: 5, salaries: [500000, 512000, 524000, 536000, 548000, 560000, 572000, 584000, 596000, 608000, 620000] },
                { grade: 5, annualAllowance: 10000, promotionYears: 5, salaries: [400000, 410000, 420000, 430000, 440000, 450000, 460000, 470000, 480000, 490000, 500000] }
            ]
        };
        localStorage.setItem('salaryScale', JSON.stringify(salaryScaleData));
    }
}

function loadAdvancedAllowanceReport() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const tableBody = document.getElementById('employeesTableBody');
    const totalEmployeesSpan = document.getElementById('totalEmployees');

    if (employees.length === 0) {
        showNoDataMessage(tableBody);
        if (totalEmployeesSpan) totalEmployeesSpan.textContent = '0';
        return;
    }

    // تصفية الموظفين المستحقين للعلاوة مع تحليل متقدم
    const eligibleEmployees = employees.filter(employee => {
        if (!employee.currentDueDate) return false;

        const dueDate = new Date(employee.currentDueDate);
        const today = new Date();

        // إظهار الموظفين المستحقين خلال السنة القادمة
        const oneYearFromNow = new Date();
        oneYearFromNow.setFullYear(today.getFullYear() + 1);

        return dueDate <= oneYearFromNow;
    });

    // إضافة تحليل مفصل لكل موظف
    const enrichedEmployees = eligibleEmployees.map(employee => {
        return enrichEmployeeData(employee);
    });

    if (totalEmployeesSpan) totalEmployeesSpan.textContent = enrichedEmployees.length;

    if (enrichedEmployees.length === 0) {
        showNoEligibleMessage(tableBody);
        return;
    }

    displayAdvancedEmployees(enrichedEmployees);
    updateReportStatistics(enrichedEmployees);
}

function enrichEmployeeData(employee) {
    const today = new Date();
    const dueDate = new Date(employee.currentDueDate);
    const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

    // البحث عن العلاوة السنوية من سلم الرواتب
    const gradeData = salaryScaleData.grades.find(g => g.grade == employee.currentDegree);
    const annualAllowance = gradeData ? gradeData.annualAllowance : 15000; // قيمة افتراضية

    // حساب عدد سنوات الخدمة
    const hireDate = new Date(employee.hireDate);
    const serviceYears = Math.floor((today - hireDate) / (1000 * 60 * 60 * 24 * 365.25));

    // تحديد حالة الاستحقاق المفصلة
    let status = '';
    let priority = 0;
    let statusClass = '';

    if (daysUntilDue < 0) {
        status = `متأخر ${Math.abs(daysUntilDue)} يوم`;
        priority = 4;
        statusClass = 'status-overdue';
    } else if (daysUntilDue <= 30) {
        status = `عاجل - خلال ${daysUntilDue} يوم`;
        priority = 3;
        statusClass = 'status-urgent';
    } else if (daysUntilDue <= 90) {
        status = `قريب - خلال ${daysUntilDue} يوم`;
        priority = 2;
        statusClass = 'status-upcoming';
    } else if (daysUntilDue <= 365) {
        status = `مستقبلي - خلال ${Math.ceil(daysUntilDue/30)} شهر`;
        priority = 1;
        statusClass = 'status-future';
    }

    return {
        ...employee,
        daysUntilDue,
        annualAllowance,
        serviceYears,
        status,
        priority,
        statusClass,
        currentSalaryFormatted: parseInt(employee.currentSalary || 0).toLocaleString(),
        allowanceFormatted: annualAllowance.toLocaleString()
    };
}

function displayAdvancedEmployees(employees) {
    const tableBody = document.getElementById('employeesTableBody');

    // ترتيب الموظفين حسب الأولوية ثم تاريخ الاستحقاق
    employees.sort((a, b) => {
        if (a.priority !== b.priority) {
            return b.priority - a.priority; // الأولوية العالية أولاً
        }
        return new Date(a.currentDueDate) - new Date(b.currentDueDate);
    });

    let html = '';
    employees.forEach((employee, index) => {
        const rowClass = getRowClass(employee.priority);

        html += `
            <tr class="${rowClass}" data-priority="${employee.priority}">
                <td class="text-center">
                    <span class="priority-badge priority-${employee.priority}">${index + 1}</span>
                </td>
                <td class="employee-info">
                    <div class="employee-name">${employee.fullName || employee.name || 'غير محدد'}</div>
                    <div class="employee-id">الرقم الوظيفي: ${employee.employeeId || 'غير محدد'}</div>
                </td>
                <td class="job-info">
                    <div class="job-description">${employee.jobDescription || 'غير محدد'}</div>
                    <div class="job-title">${employee.jobTitle || 'غير محدد'}</div>
                </td>
                <td class="work-location">${employee.workLocation || 'غير محدد'}</td>
                <td class="degree-info">
                    <div class="current-degree">الدرجة: ${employee.currentDegree || 'غير محدد'}</div>
                    <div class="current-stage">المرحلة: ${employee.currentStage || 'غير محدد'}</div>
                </td>
                <td class="salary-info">
                    <div class="current-salary">${employee.currentSalaryFormatted}</div>
                    <div class="service-years">${employee.serviceYears} سنة خدمة</div>
                </td>
                <td class="due-date">
                    <div class="date">${formatDateAdvanced(employee.currentDueDate)}</div>
                    <div class="days-info">${getDaysInfo(employee.daysUntilDue)}</div>
                </td>
                <td class="allowance-amount">
                    <div class="amount">${employee.allowanceFormatted}</div>
                    <div class="allowance-type">علاوة سنوية</div>
                </td>
                <td class="status-cell">
                    <span class="status-badge ${employee.statusClass}">${employee.status}</span>
                </td>
                <td class="actions">
                    <button class="btn-action btn-view" onclick="viewEmployeeDetails('${employee.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-action btn-print" onclick="printEmployeeReport('${employee.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}

function showNoDataMessage(tableBody) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="10" class="no-data-message">
                <div class="no-data-content">
                    <i class="fas fa-info-circle"></i>
                    <h3>لا توجد بيانات موظفين</h3>
                    <p>يرجى إضافة موظفين أولاً أو استيراد البيانات من Excel للحصول على تقارير مفصلة</p>
                    <div class="action-buttons">
                        <a href="employee-form.html" class="btn-enhanced btn-primary-enhanced">
                            <i class="fas fa-user-plus"></i> إضافة موظف جديد
                        </a>
                        <a href="import-excel.html" class="btn-enhanced btn-secondary-enhanced">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </a>
                    </div>
                </div>
            </td>
        </tr>
    `;
}

function showNoEligibleMessage(tableBody) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="10" class="no-data-message">
                <div class="no-data-content success">
                    <i class="fas fa-check-circle"></i>
                    <h3>لا يوجد موظفين مستحقين للعلاوة</h3>
                    <p>جميع الموظفين محدثين في علاواتهم السنوية</p>
                </div>
            </td>
        </tr>
    `;
}

// دوال التنسيق والمساعدة
function formatDateAdvanced(dateString) {
    if (!dateString) return 'غير محدد';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory' // التأكد من استخدام التقويم الميلادي
    };

    return date.toLocaleDateString('ar-SA', options);
}

function getDaysInfo(daysUntilDue) {
    if (daysUntilDue < 0) {
        return `متأخر ${Math.abs(daysUntilDue)} يوم`;
    } else if (daysUntilDue === 0) {
        return 'مستحق اليوم';
    } else if (daysUntilDue <= 30) {
        return `باقي ${daysUntilDue} يوم`;
    } else if (daysUntilDue <= 365) {
        const months = Math.ceil(daysUntilDue / 30);
        return `باقي ${months} شهر`;
    } else {
        const years = Math.floor(daysUntilDue / 365);
        return `باقي ${years} سنة`;
    }
}

function getRowClass(priority) {
    switch(priority) {
        case 4: return 'row-overdue';
        case 3: return 'row-urgent';
        case 2: return 'row-upcoming';
        case 1: return 'row-future';
        default: return '';
    }
}

function updateReportStatistics(employees) {
    const stats = {
        overdue: employees.filter(e => e.priority === 4).length,
        urgent: employees.filter(e => e.priority === 3).length,
        upcoming: employees.filter(e => e.priority === 2).length,
        future: employees.filter(e => e.priority === 1).length,
        totalAmount: employees.reduce((sum, e) => sum + e.annualAllowance, 0)
    };

    // تحديث الإحصائيات في الواجهة
    updateStatisticsDisplay(stats);
}

function updateStatisticsDisplay(stats) {
    // إضافة لوحة إحصائيات مفصلة
    const statsContainer = document.querySelector('.card-enhanced .card-header-enhanced');
    if (statsContainer) {
        const existingStats = statsContainer.querySelector('.report-statistics');
        if (existingStats) {
            existingStats.remove();
        }

        const statisticsHTML = `
            <div class="report-statistics">
                <div class="stat-item overdue">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="stat-number">${stats.overdue}</span>
                    <span class="stat-label">متأخر</span>
                </div>
                <div class="stat-item urgent">
                    <i class="fas fa-clock"></i>
                    <span class="stat-number">${stats.urgent}</span>
                    <span class="stat-label">عاجل</span>
                </div>
                <div class="stat-item upcoming">
                    <i class="fas fa-calendar-check"></i>
                    <span class="stat-number">${stats.upcoming}</span>
                    <span class="stat-label">قريب</span>
                </div>
                <div class="stat-item future">
                    <i class="fas fa-calendar-alt"></i>
                    <span class="stat-number">${stats.future}</span>
                    <span class="stat-label">مستقبلي</span>
                </div>
                <div class="stat-item total-amount">
                    <i class="fas fa-money-bill-wave"></i>
                    <span class="stat-number">${stats.totalAmount.toLocaleString()}</span>
                    <span class="stat-label">إجمالي العلاوات</span>
                </div>
            </div>
        `;

        statsContainer.insertAdjacentHTML('beforeend', statisticsHTML);
    }
}

function setupAdvancedEventListeners() {
    // البحث المتقدم
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            advancedFilterTable(this.value);
        });
    }

    // تصفية حسب الأولوية
    setupPriorityFilters();

    // الطباعة المتقدمة
    const printBtn = document.getElementById('printBtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printAdvancedReport();
        });
    }

    // تصدير Excel
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportToExcel();
        });
    }
}

function advancedFilterTable(searchTerm) {
    const rows = document.querySelectorAll('#employeesTableBody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());
        row.style.display = shouldShow ? '' : 'none';
    });

    // تحديث عداد النتائج
    const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
    updateSearchResults(visibleRows.length);
}

function updateSearchResults(count) {
    const totalSpan = document.getElementById('totalEmployees');
    if (totalSpan) {
        totalSpan.textContent = count;
    }
}

// دوال الإجراءات
function viewEmployeeDetails(employeeId) {
    window.open(`employee-details.html?id=${employeeId}`, '_blank');
}

function printEmployeeReport(employeeId) {
    // طباعة تقرير فردي للموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(e => e.id == employeeId);

    if (employee) {
        const enrichedEmployee = enrichEmployeeData(employee);
        printIndividualReport(enrichedEmployee);
    }
}

function printAdvancedReport() {
    window.print();
}

function exportToExcel() {
    alert('وظيفة تصدير Excel ستكون متاحة قريباً');
}
