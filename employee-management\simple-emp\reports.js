// تحميل تقرير العلاوات المستحقة
document.addEventListener('DOMContentLoaded', function() {
    loadAllowanceReport();
    setupEventListeners();
});

function loadAllowanceReport() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const tableBody = document.getElementById('employeesTableBody');
    const totalEmployeesSpan = document.getElementById('totalEmployees');
    
    if (employees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                    لا توجد بيانات موظفين. يرجى إضافة موظفين أولاً أو استيراد البيانات من Excel.
                    <br><br>
                    <a href="employee-form.html" class="btn-enhanced btn-primary-enhanced">
                        <i class="fas fa-user-plus"></i> إضافة موظف جديد
                    </a>
                    <a href="import-excel.html" class="btn-enhanced btn-secondary-enhanced" style="margin-right: 1rem;">
                        <i class="fas fa-file-excel"></i> استيراد من Excel
                    </a>
                </td>
            </tr>
        `;
        if (totalEmployeesSpan) totalEmployeesSpan.textContent = '0';
        return;
    }

    // تصفية الموظفين المستحقين للعلاوة
    const eligibleEmployees = employees.filter(employee => {
        if (!employee.currentDueDate) return false;
        
        const dueDate = new Date(employee.currentDueDate);
        const today = new Date();
        
        // إظهار الموظفين المستحقين خلال الـ 6 أشهر القادمة
        const sixMonthsFromNow = new Date();
        sixMonthsFromNow.setMonth(today.getMonth() + 6);
        
        return dueDate <= sixMonthsFromNow;
    });

    if (totalEmployeesSpan) totalEmployeesSpan.textContent = eligibleEmployees.length;

    if (eligibleEmployees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 2rem; color: #6b7280;">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem; display: block; color: #10b981;"></i>
                    لا يوجد موظفين مستحقين للعلاوة حالياً.
                </td>
            </tr>
        `;
        return;
    }

    displayEmployees(eligibleEmployees);
}

function displayEmployees(employees) {
    const tableBody = document.getElementById('employeesTableBody');
    
    // ترتيب الموظفين حسب تاريخ الاستحقاق
    employees.sort((a, b) => new Date(a.currentDueDate) - new Date(b.currentDueDate));

    let html = '';
    employees.forEach((employee, index) => {
        const dueDate = new Date(employee.currentDueDate);
        const today = new Date();
        const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
        
        // تحديد حالة الاستحقاق
        let statusClass = '';
        let statusText = '';
        let rowClass = '';
        
        if (daysUntilDue < 0) {
            statusClass = 'status-overdue';
            statusText = 'متأخر';
            rowClass = 'overdue';
        } else if (daysUntilDue <= 30) {
            statusClass = 'status-urgent';
            statusText = 'عاجل';
            rowClass = 'urgent';
        } else if (daysUntilDue <= 90) {
            statusClass = 'status-upcoming';
            statusText = 'قريب';
            rowClass = 'upcoming';
        } else {
            statusClass = 'status-future';
            statusText = 'مستقبلي';
        }

        // حساب مبلغ العلاوة المستحقة (تقديري)
        const currentSalary = parseInt(employee.currentSalary) || 0;
        const allowanceAmount = Math.round(currentSalary * 0.05); // 5% من الراتب الحالي

        html += `
            <tr class="${rowClass}">
                <td>${index + 1}</td>
                <td style="text-align: right;">${employee.fullName || employee.name || 'غير محدد'}</td>
                <td style="text-align: right;">${employee.jobDescription || 'غير محدد'}</td>
                <td style="text-align: right;">${employee.workLocation || 'غير محدد'}</td>
                <td>${currentSalary.toLocaleString()}</td>
                <td>${formatDate(employee.currentDueDate)}</td>
                <td>${allowanceAmount.toLocaleString()}</td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function setupEventListeners() {
    // البحث في الجدول
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterTable(this.value);
        });
    }

    // الطباعة
    const printBtn = document.getElementById('printBtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }
}

function filterTable(searchTerm) {
    const rows = document.querySelectorAll('#employeesTableBody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());
        row.style.display = shouldShow ? '' : 'none';
    });
}
