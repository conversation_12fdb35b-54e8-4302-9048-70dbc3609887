// تحميل تقرير العلاوات المستحقة المطور
document.addEventListener('DOMContentLoaded', function() {
    loadSalaryScale();
    loadAdvancedAllowanceReport();
    setupAdvancedEventListeners();
});

// تحميل سلم الرواتب
let salaryScaleData = null;

function loadSalaryScale() {
    const savedScale = localStorage.getItem('salaryScale');
    if (savedScale) {
        salaryScaleData = JSON.parse(savedScale);
    } else {
        // سلم الرواتب الافتراضي حسب القانون العراقي رقم 22 لسنة 2008
        salaryScaleData = {
            lastUpdate: '2025/01/01',
            grades: [
                { grade: 1, annualAllowance: 20000, promotionYears: 5, salaries: [910000, 930000, 950000, 970000, 990000, 1010000, 1030000, 1050000, 1070000, 1090000, 1110000] },
                { grade: 2, annualAllowance: 17000, promotionYears: 5, salaries: [773000, 790000, 807000, 824000, 841000, 858000, 875000, 892000, 909000, 926000, 943000] },
                { grade: 3, annualAllowance: 15000, promotionYears: 5, salaries: [600000, 610000, 620000, 630000, 640000, 650000, 660000, 670000, 680000, 690000, 700000] },
                { grade: 4, annualAllowance: 12000, promotionYears: 5, salaries: [500000, 512000, 524000, 536000, 548000, 560000, 572000, 584000, 596000, 608000, 620000] },
                { grade: 5, annualAllowance: 10000, promotionYears: 5, salaries: [400000, 410000, 420000, 430000, 440000, 450000, 460000, 470000, 480000, 490000, 500000] }
            ]
        };
        localStorage.setItem('salaryScale', JSON.stringify(salaryScaleData));
    }
}

function loadAdvancedAllowanceReport() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const tableBody = document.getElementById('employeesTableBody');
    const totalEmployeesSpan = document.getElementById('totalEmployees');

    if (employees.length === 0) {
        showNoDataMessage(tableBody);
        if (totalEmployeesSpan) totalEmployeesSpan.textContent = '0';
        return;
    }

    // تصفية الموظفين المستحقين للعلاوة مع تحليل متقدم
    const eligibleEmployees = employees.filter(employee => {
        if (!employee.currentDueDate) return false;

        const dueDate = new Date(employee.currentDueDate);
        const today = new Date();

        // إظهار الموظفين المستحقين خلال السنة القادمة
        const oneYearFromNow = new Date();
        oneYearFromNow.setFullYear(today.getFullYear() + 1);

        return dueDate <= oneYearFromNow;
    });

    // إضافة تحليل مفصل لكل موظف
    const enrichedEmployees = eligibleEmployees.map(employee => {
        return enrichEmployeeData(employee);
    });

    if (totalEmployeesSpan) totalEmployeesSpan.textContent = enrichedEmployees.length;

    if (enrichedEmployees.length === 0) {
        showNoEligibleMessage(tableBody);
        return;
    }

    displayAdvancedEmployees(enrichedEmployees);
}

function enrichEmployeeData(employee) {
    const today = new Date();
    const dueDate = new Date(employee.currentDueDate);
    const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

    // البحث عن العلاوة السنوية من سلم الرواتب
    const gradeData = salaryScaleData.grades.find(g => g.grade == employee.currentDegree);
    const annualAllowance = gradeData ? gradeData.annualAllowance : 15000; // قيمة افتراضية

    // حساب عدد سنوات الخدمة
    const hireDate = new Date(employee.hireDate);
    const serviceYears = Math.floor((today - hireDate) / (1000 * 60 * 60 * 24 * 365.25));

    // تحديد حالة الاستحقاق المفصلة
    let status = '';
    let priority = 0;
    let statusClass = '';

    if (daysUntilDue < 0) {
        status = `متأخر ${Math.abs(daysUntilDue)} يوم`;
        priority = 4;
        statusClass = 'status-overdue';
    } else if (daysUntilDue <= 30) {
        status = `عاجل - خلال ${daysUntilDue} يوم`;
        priority = 3;
        statusClass = 'status-urgent';
    } else if (daysUntilDue <= 90) {
        status = `قريب - خلال ${daysUntilDue} يوم`;
        priority = 2;
        statusClass = 'status-upcoming';
    } else if (daysUntilDue <= 365) {
        status = `مستقبلي - خلال ${Math.ceil(daysUntilDue/30)} شهر`;
        priority = 1;
        statusClass = 'status-future';
    }

    return {
        ...employee,
        daysUntilDue,
        annualAllowance,
        serviceYears,
        status,
        priority,
        statusClass,
        currentSalaryFormatted: parseInt(employee.currentSalary || 0).toLocaleString(),
        allowanceFormatted: annualAllowance.toLocaleString()
    };
}

function displayAdvancedEmployees(employees) {
    const tableBody = document.getElementById('employeesTableBody');

    // ترتيب الموظفين حسب الأولوية ثم تاريخ الاستحقاق
    employees.sort((a, b) => {
        if (a.priority !== b.priority) {
            return b.priority - a.priority; // الأولوية العالية أولاً
        }
        return new Date(a.currentDueDate) - new Date(b.currentDueDate);
    });

    let html = '';
    employees.forEach((employee, index) => {
        // حساب البيانات الجديدة
        const newDegree = employee.currentDegree;
        const newStage = Math.min(parseInt(employee.currentStage) + 1, 10);
        const newSalary = calculateNewSalary(employee.currentDegree, newStage);
        const newDueDate = calculateNextDueDate(employee.currentDueDate);

        html += `
            <tr>
                <td>${employee.employeeId || 'غير محدد'}</td>
                <td>${employee.fullName || employee.name || 'غير محدد'}</td>
                <td>${employee.workLocation || 'غير محدد'}</td>
                <td>${employee.currentDegree || 'غير محدد'}</td>
                <td>${employee.currentStage || 'غير محدد'}</td>
                <td>${employee.currentSalary || 0}</td>
                <td>${formatDateAdvanced(employee.currentDueDate)}</td>
                <td>${newDegree}</td>
                <td>${newStage}</td>
                <td>${newSalary}</td>
                <td>${formatDateAdvanced(newDueDate)}</td>
                <td>${employee.annualAllowance || 0}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn btn-current">الراتب الحالي</button>
                        <button class="action-btn btn-new">الراتب الجديد</button>
                        <button class="action-btn btn-allowance">العلاوة</button>
                        <button class="action-btn btn-promotion">الترفيع</button>
                        <button class="action-btn btn-details">التفاصيل</button>
                        <button class="action-btn btn-edit">تعديل</button>
                    </div>
                </td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}

function calculateNewSalary(degree, stage) {
    // استخدام سلم الرواتب العراقي
    const salaryScale = {
        1: [800000, 820000, 840000, 860000, 880000, 900000, 920000, 940000, 960000, 980000],
        2: [700000, 720000, 740000, 760000, 780000, 800000, 820000, 840000, 860000, 880000],
        3: [600000, 620000, 640000, 660000, 680000, 700000, 720000, 740000, 760000, 780000],
        4: [500000, 520000, 540000, 560000, 580000, 600000, 620000, 640000, 660000, 680000],
        5: [400000, 420000, 440000, 460000, 480000, 500000, 520000, 540000, 560000, 580000],
        6: [350000, 370000, 390000, 410000, 430000, 450000, 470000, 490000, 510000, 530000],
        7: [300000, 320000, 340000, 360000, 380000, 400000, 420000, 440000, 460000, 480000],
        8: [250000, 270000, 290000, 310000, 330000, 350000, 370000, 390000, 410000, 430000],
        9: [200000, 220000, 240000, 260000, 280000, 300000, 320000, 340000, 360000, 380000],
        10: [150000, 170000, 190000, 210000, 230000, 250000, 270000, 290000, 310000, 330000]
    };

    return salaryScale[degree] ? salaryScale[degree][stage - 1] || 0 : 0;
}

function calculateNextDueDate(currentDue) {
    const date = new Date(currentDue);
    date.setFullYear(date.getFullYear() + 1);
    return date.toISOString().split('T')[0];
}

function showNoDataMessage(tableBody) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="10" class="no-data-message">
                <div class="no-data-content">
                    <i class="fas fa-info-circle"></i>
                    <h3>لا توجد بيانات موظفين</h3>
                    <p>يرجى إضافة موظفين أولاً أو استيراد البيانات من Excel للحصول على تقارير مفصلة</p>
                    <div class="action-buttons">
                        <a href="employee-form.html" class="btn-enhanced btn-primary-enhanced">
                            <i class="fas fa-user-plus"></i> إضافة موظف جديد
                        </a>
                        <a href="import-excel.html" class="btn-enhanced btn-secondary-enhanced">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </a>
                    </div>
                </div>
            </td>
        </tr>
    `;
}

function showNoEligibleMessage(tableBody) {
    tableBody.innerHTML = `
        <tr>
            <td colspan="10" class="no-data-message">
                <div class="no-data-content success">
                    <i class="fas fa-check-circle"></i>
                    <h3>لا يوجد موظفين مستحقين للعلاوة</h3>
                    <p>جميع الموظفين محدثين في علاواتهم السنوية</p>
                </div>
            </td>
        </tr>
    `;
}

// دوال التنسيق والمساعدة
function formatDateAdvanced(dateString) {
    if (!dateString) return 'غير محدد';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory' // التأكد من استخدام التقويم الميلادي
    };

    return date.toLocaleDateString('ar-SA', options);
}

function getDaysInfo(daysUntilDue) {
    if (daysUntilDue < 0) {
        return `متأخر ${Math.abs(daysUntilDue)} يوم`;
    } else if (daysUntilDue === 0) {
        return 'مستحق اليوم';
    } else if (daysUntilDue <= 30) {
        return `باقي ${daysUntilDue} يوم`;
    } else if (daysUntilDue <= 365) {
        const months = Math.ceil(daysUntilDue / 30);
        return `باقي ${months} شهر`;
    } else {
        const years = Math.floor(daysUntilDue / 365);
        return `باقي ${years} سنة`;
    }
}

function getRowClass(priority) {
    switch(priority) {
        case 4: return 'row-overdue';
        case 3: return 'row-urgent';
        case 2: return 'row-upcoming';
        case 1: return 'row-future';
        default: return '';
    }
}

// تم حذف دوال الإحصائيات

function setupAdvancedEventListeners() {
    // البحث المتقدم
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            advancedFilterTable(this.value);
        });
    }

    // تصفية حسب الأولوية
    setupPriorityFilters();

    // الطباعة المتقدمة
    const printBtn = document.getElementById('printBtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printAdvancedReport();
        });
    }

    // تصدير Excel
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportToExcel();
        });
    }
}

function advancedFilterTable(searchTerm) {
    const rows = document.querySelectorAll('#employeesTableBody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const shouldShow = text.includes(searchTerm.toLowerCase());
        row.style.display = shouldShow ? '' : 'none';
    });

    // تحديث عداد النتائج
    const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
    updateSearchResults(visibleRows.length);
}

function updateSearchResults(count) {
    const totalSpan = document.getElementById('totalEmployees');
    if (totalSpan) {
        totalSpan.textContent = count;
    }
}

// دوال الإجراءات
function viewEmployeeDetails(employeeId) {
    window.open(`employee-details.html?id=${employeeId}`, '_blank');
}

function printEmployeeReport(employeeId) {
    // طباعة تقرير فردي للموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(e => e.id == employeeId);

    if (employee) {
        const enrichedEmployee = enrichEmployeeData(employee);
        printIndividualReport(enrichedEmployee);
    }
}

function printAdvancedReport() {
    window.print();
}

function exportToExcel() {
    alert('وظيفة تصدير Excel ستكون متاحة قريباً');
}
