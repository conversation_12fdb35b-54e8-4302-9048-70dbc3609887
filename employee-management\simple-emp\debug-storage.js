/**
 * ملف للتحقق من بيانات التخزين المحلي
 */

// عرض محتويات التخزين المحلي في وحدة التحكم
function displayLocalStorage() {
    console.log('=== محتويات التخزين المحلي ===');

    // عرض العناوين الوظيفية
    const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    console.log('العناوين الوظيفية:', jobTitles);

    // عرض مواقع العمل
    const workLocations = JSON.parse(localStorage.getItem('workLocations') || '[]');
    console.log('مواقع العمل:', workLocations);

    // عرض التحصيل الدراسي
    const educationLevels = JSON.parse(localStorage.getItem('educationLevels') || '[]');
    console.log('التحصيل الدراسي:', educationLevels);

    // عرض الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    console.log('الموظفين:', employees);

    console.log('=== نهاية محتويات التخزين المحلي ===');
}

// إعادة تعيين العناوين الوظيفية إلى قائمة فارغة
function resetJobTitles() {
    const defaultJobTitles = [];

    localStorage.setItem('jobTitles', JSON.stringify(defaultJobTitles));
    console.log('تم إعادة تعيين العناوين الوظيفية إلى قائمة فارغة');
    displayLocalStorage();
}

// إعادة تعيين مواقع العمل إلى القيم الافتراضية
function resetWorkLocations() {
    const defaultWorkLocations = [
        { id: 1, name: 'كلية الهندسة' },
        { id: 2, name: 'كلية العلوم' },
        { id: 3, name: 'كلية الطب' },
        { id: 4, name: 'كلية الآداب' },
        { id: 5, name: 'رئاسة الجامعة' }
    ];

    localStorage.setItem('workLocations', JSON.stringify(defaultWorkLocations));
    console.log('تم إعادة تعيين مواقع العمل إلى القيم الافتراضية');
    displayLocalStorage();
}

// إعادة تعيين التحصيل الدراسي إلى القيم الافتراضية
function resetEducationLevels() {
    const defaultEducationLevels = [
        { id: 1, name: 'دبلوم' },
        { id: 2, name: 'بكالوريوس' },
        { id: 3, name: 'ماجستير' },
        { id: 4, name: 'دكتوراه' }
    ];

    localStorage.setItem('educationLevels', JSON.stringify(defaultEducationLevels));
    console.log('تم إعادة تعيين التحصيل الدراسي إلى القيم الافتراضية');
    displayLocalStorage();
}

// إعادة تعيين جميع البيانات إلى القيم الافتراضية
function resetAllData() {
    resetJobTitles();
    resetWorkLocations();
    resetEducationLevels();
    localStorage.removeItem('employees');
    console.log('تم إعادة تعيين جميع البيانات إلى القيم الافتراضية');
    displayLocalStorage();
}

// عرض محتويات التخزين المحلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    displayLocalStorage();

    // تم إزالة أدوات التصحيح من الواجهة
    // يمكن استدعاء الوظائف من وحدة التحكم عند الحاجة
});
