<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كتب الشكر - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        .simple-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .simple-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: end;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group select,
        .form-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .simple-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .simple-table th,
        .simple-table td {
            padding: 10px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .simple-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .simple-table tr:hover {
            background: #f8f9fa;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        .thanks-type {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .type-appreciation {
            background: #d4edda;
            color: #155724;
        }

        .type-ministerial {
            background: #fff3cd;
            color: #856404;
        }

        .type-presidential {
            background: #d1ecf1;
            color: #0c5460;
        }

        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="simple-container">
        <h1><i class="fas fa-award"></i> كتب الشكر والتقدير</h1>

        <!-- نموذج إضافة كتاب شكر -->
        <div class="simple-form">
            <h3>إضافة كتاب شكر جديد</h3>
            <form id="thanksForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="employeeSelect">الموظف</label>
                        <select id="employeeSelect" required>
                            <option value="">اختر الموظف</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="thanksTypeSelect">نوع كتاب الشكر</label>
                        <select id="thanksTypeSelect" required>
                            <option value="">اختر النوع</option>
                            <option value="appreciation">شكر جامعي (شهر واحد)</option>
                            <option value="ministerial">شكر وزاري (شهر واحد)</option>
                            <option value="presidential">شكر رئاسي (6 أشهر)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="thanksDate">التاريخ</label>
                        <input type="date" id="thanksDate" required>
                    </div>

                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- رسائل النظام -->
        <div id="messageArea"></div>

        <!-- جدول كتب الشكر -->
        <div>
            <h3>قائمة كتب الشكر</h3>
            <table class="simple-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>نوع الشكر</th>
                        <th>التاريخ</th>
                        <th>التأثير</th>
                        <th>حذف</th>
                    </tr>
                </thead>
                <tbody id="thanksTableBody">
                    <!-- سيتم ملء البيانات هنا -->
                </tbody>
            </table>
        </div>

        <!-- زر تحديث جميع الموظفين -->
        <div style="text-align: center; margin-top: 20px;">
            <button id="updateAllBtn" class="btn btn-primary">
                <i class="fas fa-sync"></i> تحديث تواريخ جميع الموظفين
            </button>
        </div>
    </div>

    <script src="employees-data.js"></script>
    <script src="thanks-calculator.js"></script>
    <script>
        // متغيرات عامة
        let employees = [];
        let thanks = [];

        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            setupEventListeners();
            displayEmployees();
            displayThanks();
            setTodayDate();
        });

        // تحميل البيانات من التخزين المحلي
        function loadData() {
            employees = JSON.parse(localStorage.getItem('employees') || '[]');
            thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        }

        // حفظ البيانات في التخزين المحلي
        function saveData() {
            localStorage.setItem('thanks', JSON.stringify(thanks));
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            document.getElementById('thanksForm').addEventListener('submit', addThanks);
            document.getElementById('updateAllBtn').addEventListener('click', updateAllEmployees);
        }

        // عرض الموظفين في القائمة المنسدلة
        function displayEmployees() {
            const select = document.getElementById('employeeSelect');
            select.innerHTML = '<option value="">اختر الموظف</option>';

            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = employee.name;
                select.appendChild(option);
            });
        }

        // عرض كتب الشكر في الجدول
        function displayThanks() {
            const tbody = document.getElementById('thanksTableBody');
            tbody.innerHTML = '';

            if (thanks.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد كتب شكر</td></tr>';
                return;
            }

            thanks.forEach((thank, index) => {
                const employee = employees.find(emp => emp.id == thank.employeeId);
                const employeeName = employee ? employee.name : 'غير معروف';

                let typeText = '';
                let typeClass = '';
                let effect = '';

                switch (thank.type) {
                    case 'appreciation':
                        typeText = 'شكر جامعي';
                        typeClass = 'type-appreciation';
                        effect = 'شهر واحد';
                        break;
                    case 'ministerial':
                        typeText = 'شكر وزاري';
                        typeClass = 'type-ministerial';
                        effect = 'شهر واحد';
                        break;
                    case 'presidential':
                        typeText = 'شكر رئاسي';
                        typeClass = 'type-presidential';
                        effect = '6 أشهر';
                        break;
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${employeeName}</td>
                    <td><span class="thanks-type ${typeClass}">${typeText}</span></td>
                    <td>${formatDate(thank.date)}</td>
                    <td>${effect}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteThanks(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // إضافة كتاب شكر جديد
        function addThanks(event) {
            event.preventDefault();

            const employeeId = document.getElementById('employeeSelect').value;
            const type = document.getElementById('thanksTypeSelect').value;
            const date = document.getElementById('thanksDate').value;

            if (!employeeId || !type || !date) {
                showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const thank = {
                id: Date.now(),
                employeeId: parseInt(employeeId),
                type: type,
                date: date
            };

            thanks.push(thank);
            saveData();

            // تحديث تواريخ الموظف
            if (window.thanksCalculator) {
                window.thanksCalculator.updateNewDueDateBasedOnThanks(employeeId);
                window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employeeId);
            }

            displayThanks();
            document.getElementById('thanksForm').reset();
            setTodayDate();

            const employee = employees.find(emp => emp.id == employeeId);
            showMessage(`تم إضافة كتاب شكر للموظف ${employee.name} بنجاح`, 'success');
        }

        // حذف كتاب شكر
        function deleteThanks(index) {
            if (confirm('هل أنت متأكد من حذف كتاب الشكر؟')) {
                const employeeId = thanks[index].employeeId;
                thanks.splice(index, 1);
                saveData();

                // تحديث تواريخ الموظف
                if (window.thanksCalculator) {
                    window.thanksCalculator.updateNewDueDateBasedOnThanks(employeeId);
                    window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employeeId);
                }

                displayThanks();
                showMessage('تم حذف كتاب الشكر بنجاح', 'success');
            }
        }

        // تحديث تواريخ جميع الموظفين
        function updateAllEmployees() {
            if (confirm('هل تريد تحديث تواريخ جميع الموظفين؟')) {
                let updatedCount = 0;

                employees.forEach(employee => {
                    if (window.thanksCalculator) {
                        window.thanksCalculator.updateNewDueDateBasedOnThanks(employee.id);
                        window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employee.id);
                        updatedCount++;
                    }
                });

                showMessage(`تم تحديث تواريخ ${updatedCount} موظف بنجاح`, 'success');
            }
        }

        // تعيين تاريخ اليوم
        function setTodayDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('thanksDate').value = today;
        }

        // تنسيق التاريخ (ميلادي فقط)
        function formatDate(dateString) {
            const date = new Date(dateString);
            // استخدام التقويم الميلادي بوضوح
            return date.toLocaleDateString('ar-SA-u-ca-gregory', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            });
        }

        // عرض رسالة
        function showMessage(text, type) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = `<div class="message ${type}">${text}</div>`;

            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
