// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة تنبيهات التقاعد
    initRetirementAlertsPage();
});

// تهيئة صفحة تنبيهات التقاعد
function initRetirementAlertsPage() {
    // تحميل إعدادات التنبيهات
    loadRetirementAlertSettings();
    
    // تحميل تنبيهات التقاعد
    loadRetirementAlerts();
    
    // تهيئة نموذج الإعدادات
    initRetirementAlertSettingsForm();
    
    // تهيئة البحث
    initRetirementSearch();
    
    // تهيئة أزرار الإجراءات
    initRetirementActionButtons();
}

// تحميل إعدادات تنبيهات التقاعد
function loadRetirementAlertSettings() {
    const settings = JSON.parse(localStorage.getItem('retirementAlertSettings') || '{}');
    
    // تطبيق الإعدادات المحفوظة
    const alertPeriodSelect = document.getElementById('alertPeriod');
    const customDaysInput = document.getElementById('customDays');
    const notificationTypeSelect = document.getElementById('notificationType');
    
    if (alertPeriodSelect && settings.alertPeriod) {
        alertPeriodSelect.value = settings.alertPeriod;
        
        // إذا كانت الفترة مخصصة، فعّل حقل الأيام المخصصة
        if (settings.alertPeriod === 'custom' && customDaysInput) {
            customDaysInput.disabled = false;
            customDaysInput.value = settings.customDays || 90;
        }
    }
    
    if (notificationTypeSelect && settings.notificationType) {
        notificationTypeSelect.value = settings.notificationType;
    }
}

// تحميل تنبيهات التقاعد
function loadRetirementAlerts() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const settings = JSON.parse(localStorage.getItem('retirementAlertSettings') || '{}');
    
    // تحديد فترة التنبيه (افتراضي 90 يوم للتقاعد)
    let alertPeriod = 90;
    if (settings.alertPeriod === 'custom') {
        alertPeriod = parseInt(settings.customDays) || 90;
    } else if (settings.alertPeriod) {
        alertPeriod = parseInt(settings.alertPeriod);
    }
    
    const today = new Date();
    const alerts = [];
    
    // إنشاء تنبيهات للموظفين المقاربين للتقاعد
    employees.forEach(employee => {
        if (employee.retirementDate) {
            const retirementDate = new Date(employee.retirementDate);
            const diffTime = retirementDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays <= alertPeriod && diffDays >= 0) {
                alerts.push({
                    id: employee.id,
                    employeeName: employee.name,
                    employeeNumber: employee.employeeNumber || employee.id,
                    workLocation: employee.workLocation || 'غير محدد',
                    currentJobTitle: employee.currentJobTitle || 'غير محدد',
                    retirementDate: employee.retirementDate,
                    daysRemaining: diffDays,
                    birthDate: employee.birthDate,
                    serviceYears: calculateServiceYears(employee.appointmentDate),
                    retirementType: determineRetirementType(employee)
                });
            }
        }
    });
    
    // ترتيب التنبيهات حسب الأولوية (الأقرب أولاً)
    alerts.sort((a, b) => a.daysRemaining - b.daysRemaining);
    
    // عرض التنبيهات
    displayRetirementAlerts(alerts);
    
    // تحديث العداد
    updateRetirementAlertsCount(alerts.length);
}

// عرض تنبيهات التقاعد
function displayRetirementAlerts(alerts) {
    const alertsContainer = document.querySelector('.alerts-container');
    if (!alertsContainer) return;
    
    alertsContainer.innerHTML = '';
    
    if (alerts.length === 0) {
        alertsContainer.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #6b7280;">
                <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>لا توجد تنبيهات تقاعد في الوقت الحالي</p>
            </div>
        `;
        return;
    }
    
    alerts.forEach(alert => {
        const alertElement = createRetirementAlertElement(alert);
        alertsContainer.appendChild(alertElement);
    });
}

// إنشاء عنصر تنبيه التقاعد
function createRetirementAlertElement(alert) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert-item';
    alertDiv.style.cssText = `
        background-color: #f3f4f6; 
        border-radius: 12px; 
        padding: 1rem; 
        border-right: 4px solid #f59e0b; 
        display: flex; 
        justify-content: space-between; 
        align-items: center;
        margin-bottom: 1rem;
    `;
    
    // تحديد لون الحدود حسب الأولوية
    let borderColor = '#f59e0b'; // أصفر افتراضي
    if (alert.daysRemaining <= 30) {
        borderColor = '#ef4444'; // أحمر للعاجل
    } else if (alert.daysRemaining <= 60) {
        borderColor = '#f97316'; // برتقالي للمتوسط
    }
    
    alertDiv.style.borderRightColor = borderColor;
    
    alertDiv.innerHTML = `
        <div class="alert-content">
            <div class="alert-title" style="font-weight: 600; font-size: 1.1rem; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-bell" style="color: ${borderColor};"></i>
                إحالة إلى التقاعد للموظف: ${alert.employeeName}
            </div>
            <div class="alert-details" style="display: flex; gap: 2rem; color: #4b5563; font-size: 0.9rem; flex-wrap: wrap;">
                <div>
                    <i class="fas fa-id-card" style="margin-left: 0.3rem;"></i>
                    الرقم الوظيفي: ${alert.employeeNumber}
                </div>
                <div>
                    <i class="fas fa-calendar-alt" style="margin-left: 0.3rem;"></i>
                    تاريخ التقاعد: ${formatDate(alert.retirementDate)}
                </div>
                <div>
                    <i class="fas fa-clock" style="margin-left: 0.3rem;"></i>
                    متبقي: ${alert.daysRemaining} يوم
                </div>
                <div>
                    <i class="fas fa-building" style="margin-left: 0.3rem;"></i>
                    موقع العمل: ${alert.workLocation}
                </div>
                <div>
                    <i class="fas fa-user-tie" style="margin-left: 0.3rem;"></i>
                    المنصب: ${alert.currentJobTitle}
                </div>
                <div>
                    <i class="fas fa-calendar-check" style="margin-left: 0.3rem;"></i>
                    سنوات الخدمة: ${alert.serviceYears} سنة
                </div>
                <div>
                    <i class="fas fa-info-circle" style="margin-left: 0.3rem;"></i>
                    نوع التقاعد: ${alert.retirementType}
                </div>
            </div>
        </div>
        <div class="alert-actions" style="display: flex; gap: 0.5rem;">
            <button class="btn-enhanced btn-primary-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="viewEmployeeDetails('${alert.id}')" title="عرض تفاصيل الموظف">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn-enhanced btn-warning-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="prepareRetirement('${alert.id}')" title="تحضير ملف التقاعد">
                <i class="fas fa-file-alt"></i>
            </button>
            <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="dismissRetirementAlert('${alert.id}')" title="تجاهل التنبيه">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    return alertDiv;
}

// تهيئة نموذج إعدادات تنبيهات التقاعد
function initRetirementAlertSettingsForm() {
    const form = document.getElementById('alertSettingsForm');
    const alertPeriodSelect = document.getElementById('alertPeriod');
    const customDaysInput = document.getElementById('customDays');
    
    if (alertPeriodSelect) {
        alertPeriodSelect.addEventListener('change', function() {
            if (customDaysInput) {
                customDaysInput.disabled = this.value !== 'custom';
                if (this.value === 'custom') {
                    customDaysInput.focus();
                }
            }
        });
    }
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            saveRetirementAlertSettings();
        });
    }
}

// حفظ إعدادات تنبيهات التقاعد
function saveRetirementAlertSettings() {
    const alertPeriod = document.getElementById('alertPeriod').value;
    const customDays = document.getElementById('customDays').value;
    const notificationType = document.getElementById('notificationType').value;
    
    const settings = {
        alertPeriod: alertPeriod,
        customDays: parseInt(customDays),
        notificationType: notificationType,
        lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem('retirementAlertSettings', JSON.stringify(settings));
    
    // إعادة تحميل التنبيهات بالإعدادات الجديدة
    loadRetirementAlerts();
    
    // عرض رسالة نجاح
    showMessage('تم حفظ إعدادات التنبيهات بنجاح', 'success');
}

// تهيئة البحث
function initRetirementSearch() {
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterRetirementAlerts(this.value);
        });
    }
}

// تصفية تنبيهات التقاعد
function filterRetirementAlerts(searchTerm) {
    const alertItems = document.querySelectorAll('.alert-item');
    const term = searchTerm.toLowerCase().trim();
    
    alertItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(term)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// تهيئة أزرار الإجراءات
function initRetirementActionButtons() {
    // سيتم إضافة المزيد من الوظائف هنا حسب الحاجة
}

// عرض تفاصيل الموظف
function viewEmployeeDetails(employeeId) {
    window.location.href = `employee-form.html?id=${employeeId}&mode=view`;
}

// تحضير ملف التقاعد
function prepareRetirement(employeeId) {
    if (confirm('هل تريد تحضير ملف التقاعد لهذا الموظف؟')) {
        // يمكن إضافة منطق لإنشاء ملف التقاعد
        showMessage('سيتم تحضير ملف التقاعد', 'info');
        // window.location.href = `retirement-file.html?id=${employeeId}`;
    }
}

// تجاهل تنبيه التقاعد
function dismissRetirementAlert(employeeId) {
    if (confirm('هل تريد تجاهل هذا التنبيه؟')) {
        // يمكن إضافة منطق لحفظ التنبيهات المتجاهلة
        showMessage('تم تجاهل التنبيه', 'info');
        loadRetirementAlerts(); // إعادة تحميل التنبيهات
    }
}

// حساب سنوات الخدمة
function calculateServiceYears(appointmentDate) {
    if (!appointmentDate) return 0;
    
    const appointment = new Date(appointmentDate);
    const today = new Date();
    const diffTime = today - appointment;
    const diffYears = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));
    
    return diffYears;
}

// تحديد نوع التقاعد
function determineRetirementType(employee) {
    const serviceYears = calculateServiceYears(employee.appointmentDate);
    const currentAge = calculateAge(employee.birthDate);
    
    if (currentAge >= 63) {
        return 'تقاعد اختياري (السن القانوني)';
    } else if (serviceYears >= 25) {
        return 'تقاعد اختياري (25 سنة خدمة)';
    } else if (serviceYears >= 15 && currentAge >= 50) {
        return 'تقاعد اختياري (15 سنة + 50 سنة)';
    } else {
        return 'تقاعد إجباري';
    }
}

// حساب العمر
function calculateAge(birthDate) {
    if (!birthDate) return 0;
    
    const birth = new Date(birthDate);
    const today = new Date();
    const diffTime = today - birth;
    const age = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25));
    
    return age;
}

// تحديث عداد تنبيهات التقاعد
function updateRetirementAlertsCount(count) {
    const totalAlertsSpan = document.getElementById('totalAlerts');
    if (totalAlertsSpan) {
        totalAlertsSpan.textContent = count;
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}
