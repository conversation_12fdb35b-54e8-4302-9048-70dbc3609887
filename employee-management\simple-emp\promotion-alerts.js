// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة تنبيهات الترفيعات
    initPromotionAlertsPage();
});

// تهيئة صفحة تنبيهات الترفيعات
function initPromotionAlertsPage() {
    // تحميل إعدادات التنبيهات
    loadPromotionAlertSettings();
    
    // تحميل تنبيهات الترفيعات
    loadPromotionAlerts();
    
    // تهيئة نموذج الإعدادات
    initPromotionAlertSettingsForm();
    
    // تهيئة البحث
    initPromotionSearch();
    
    // تهيئة أزرار الإجراءات
    initPromotionActionButtons();
}

// تحميل إعدادات تنبيهات الترفيعات
function loadPromotionAlertSettings() {
    const settings = JSON.parse(localStorage.getItem('promotionAlertSettings') || '{}');
    
    // تطبيق الإعدادات المحفوظة
    const alertPeriodSelect = document.getElementById('alertPeriod');
    const customDaysInput = document.getElementById('customDays');
    const notificationTypeSelect = document.getElementById('notificationType');
    
    if (alertPeriodSelect && settings.alertPeriod) {
        alertPeriodSelect.value = settings.alertPeriod;
        
        // إذا كانت الفترة مخصصة، فعّل حقل الأيام المخصصة
        if (settings.alertPeriod === 'custom' && customDaysInput) {
            customDaysInput.disabled = false;
            customDaysInput.value = settings.customDays || 60;
        }
    }
    
    if (notificationTypeSelect && settings.notificationType) {
        notificationTypeSelect.value = settings.notificationType;
    }
}

// تحميل تنبيهات الترفيعات
function loadPromotionAlerts() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const settings = JSON.parse(localStorage.getItem('promotionAlertSettings') || '{}');
    
    // تحديد فترة التنبيه (افتراضي 60 يوم للترفيعات)
    let alertPeriod = 60;
    if (settings.alertPeriod === 'custom') {
        alertPeriod = parseInt(settings.customDays) || 60;
    } else if (settings.alertPeriod) {
        alertPeriod = parseInt(settings.alertPeriod);
    }
    
    const today = new Date();
    const alerts = [];
    
    // إنشاء تنبيهات للموظفين المستحقين للترفيع
    employees.forEach(employee => {
        if (employee.nextPromotionDate) {
            const nextPromotionDate = new Date(employee.nextPromotionDate);
            const diffTime = nextPromotionDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays <= alertPeriod && diffDays >= 0) {
                alerts.push({
                    id: employee.id,
                    employeeName: employee.name,
                    employeeNumber: employee.employeeNumber || employee.id,
                    workLocation: employee.workLocation || 'غير محدد',
                    currentJobTitle: employee.currentJobTitle || 'غير محدد',
                    newJobTitle: employee.newJobTitle || 'غير محدد',
                    nextPromotionDate: employee.nextPromotionDate,
                    daysRemaining: diffDays,
                    currentDegree: employee.currentDegree || 'غير محدد',
                    newDegree: employee.newDegree || 'غير محدد'
                });
            }
        }
    });
    
    // ترتيب التنبيهات حسب الأولوية (الأقرب أولاً)
    alerts.sort((a, b) => a.daysRemaining - b.daysRemaining);
    
    // عرض التنبيهات
    displayPromotionAlerts(alerts);
    
    // تحديث العداد
    updatePromotionAlertsCount(alerts.length);
}

// عرض تنبيهات الترفيعات
function displayPromotionAlerts(alerts) {
    const alertsContainer = document.querySelector('.alerts-container');
    if (!alertsContainer) return;
    
    alertsContainer.innerHTML = '';
    
    if (alerts.length === 0) {
        alertsContainer.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #6b7280;">
                <i class="fas fa-bell-slash" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>لا توجد تنبيهات ترفيعات في الوقت الحالي</p>
            </div>
        `;
        return;
    }
    
    alerts.forEach(alert => {
        const alertElement = createPromotionAlertElement(alert);
        alertsContainer.appendChild(alertElement);
    });
}

// إنشاء عنصر تنبيه الترفيع
function createPromotionAlertElement(alert) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert-item';
    alertDiv.style.cssText = `
        background-color: #f3f4f6; 
        border-radius: 12px; 
        padding: 1rem; 
        border-right: 4px solid #10b981; 
        display: flex; 
        justify-content: space-between; 
        align-items: center;
        margin-bottom: 1rem;
    `;
    
    // تحديد لون الحدود حسب الأولوية
    let borderColor = '#10b981'; // أخضر افتراضي
    if (alert.daysRemaining <= 7) {
        borderColor = '#ef4444'; // أحمر للعاجل
    } else if (alert.daysRemaining <= 30) {
        borderColor = '#f59e0b'; // أصفر للمتوسط
    }
    
    alertDiv.style.borderRightColor = borderColor;
    
    alertDiv.innerHTML = `
        <div class="alert-content">
            <div class="alert-title" style="font-weight: 600; font-size: 1.1rem; margin-bottom: 0.5rem; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-bell" style="color: ${borderColor};"></i>
                ترفيع مستحق للموظف: ${alert.employeeName}
            </div>
            <div class="alert-details" style="display: flex; gap: 2rem; color: #4b5563; font-size: 0.9rem; flex-wrap: wrap;">
                <div>
                    <i class="fas fa-id-card" style="margin-left: 0.3rem;"></i>
                    الرقم الوظيفي: ${alert.employeeNumber}
                </div>
                <div>
                    <i class="fas fa-calendar-alt" style="margin-left: 0.3rem;"></i>
                    تاريخ الاستحقاق: ${formatDate(alert.nextPromotionDate)}
                </div>
                <div>
                    <i class="fas fa-clock" style="margin-left: 0.3rem;"></i>
                    متبقي: ${alert.daysRemaining} يوم
                </div>
                <div>
                    <i class="fas fa-building" style="margin-left: 0.3rem;"></i>
                    موقع العمل: ${alert.workLocation}
                </div>
                <div>
                    <i class="fas fa-user-tie" style="margin-left: 0.3rem;"></i>
                    من: ${alert.currentJobTitle} إلى: ${alert.newJobTitle}
                </div>
                <div>
                    <i class="fas fa-star" style="margin-left: 0.3rem;"></i>
                    من الدرجة: ${alert.currentDegree} إلى: ${alert.newDegree}
                </div>
            </div>
        </div>
        <div class="alert-actions" style="display: flex; gap: 0.5rem;">
            <button class="btn-enhanced btn-primary-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="viewEmployeeDetails('${alert.id}')" title="عرض تفاصيل الموظف">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn-enhanced btn-success-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="processPromotion('${alert.id}')" title="معالجة الترفيع">
                <i class="fas fa-check"></i>
            </button>
            <button class="btn-enhanced btn-outline-enhanced" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;" 
                    onclick="dismissPromotionAlert('${alert.id}')" title="تجاهل التنبيه">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    return alertDiv;
}

// تهيئة نموذج إعدادات تنبيهات الترفيعات
function initPromotionAlertSettingsForm() {
    const form = document.getElementById('alertSettingsForm');
    const alertPeriodSelect = document.getElementById('alertPeriod');
    const customDaysInput = document.getElementById('customDays');
    
    if (alertPeriodSelect) {
        alertPeriodSelect.addEventListener('change', function() {
            if (customDaysInput) {
                customDaysInput.disabled = this.value !== 'custom';
                if (this.value === 'custom') {
                    customDaysInput.focus();
                }
            }
        });
    }
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            savePromotionAlertSettings();
        });
    }
}

// حفظ إعدادات تنبيهات الترفيعات
function savePromotionAlertSettings() {
    const alertPeriod = document.getElementById('alertPeriod').value;
    const customDays = document.getElementById('customDays').value;
    const notificationType = document.getElementById('notificationType').value;
    
    const settings = {
        alertPeriod: alertPeriod,
        customDays: parseInt(customDays),
        notificationType: notificationType,
        lastUpdated: new Date().toISOString()
    };
    
    localStorage.setItem('promotionAlertSettings', JSON.stringify(settings));
    
    // إعادة تحميل التنبيهات بالإعدادات الجديدة
    loadPromotionAlerts();
    
    // عرض رسالة نجاح
    showMessage('تم حفظ إعدادات التنبيهات بنجاح', 'success');
}

// تهيئة البحث
function initPromotionSearch() {
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterPromotionAlerts(this.value);
        });
    }
}

// تصفية تنبيهات الترفيعات
function filterPromotionAlerts(searchTerm) {
    const alertItems = document.querySelectorAll('.alert-item');
    const term = searchTerm.toLowerCase().trim();
    
    alertItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(term)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// تهيئة أزرار الإجراءات
function initPromotionActionButtons() {
    // سيتم إضافة المزيد من الوظائف هنا حسب الحاجة
}

// عرض تفاصيل الموظف
function viewEmployeeDetails(employeeId) {
    window.location.href = `employee-form.html?id=${employeeId}&mode=view`;
}

// معالجة الترفيع
function processPromotion(employeeId) {
    if (confirm('هل تريد معالجة الترفيع لهذا الموظف؟')) {
        window.location.href = `employee-form.html?id=${employeeId}&action=promotion`;
    }
}

// تجاهل تنبيه الترفيع
function dismissPromotionAlert(employeeId) {
    if (confirm('هل تريد تجاهل هذا التنبيه؟')) {
        // يمكن إضافة منطق لحفظ التنبيهات المتجاهلة
        showMessage('تم تجاهل التنبيه', 'info');
        loadPromotionAlerts(); // إعادة تحميل التنبيهات
    }
}

// تحديث عداد تنبيهات الترفيعات
function updatePromotionAlertsCount(count) {
    const totalAlertsSpan = document.getElementById('totalAlerts');
    if (totalAlertsSpan) {
        totalAlertsSpan.textContent = count;
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}
