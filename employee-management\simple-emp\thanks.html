<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التشكرات - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط خاصة بصفحة التشكرات والعقوبات -->
    <link rel="stylesheet" href="thanks-penalties.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">
    <!-- إضافة أنماط تنظيم كتب الشكر -->
    <link rel="stylesheet" href="thanks-organized.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                برنامج إدارة العلاوات والترفيعات
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="thanks.html" class="active"><i class="fas fa-award"></i> التشكرات</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="thanks-container">
            <h1 class="page-title">إدارة التشكرات</h1>

            <div class="thanks-content">
                <div class="thanks-form-section">
                    <h2><i class="fas fa-plus-circle"></i> إضافة تشكر جديد</h2>
                    <form id="addThanksForm" class="thanks-form">
                        <div class="form-group">
                            <label for="employeeSelection">اختيار الموظفين:</label>
                            <select id="employeeSelection" name="employeeSelection">
                                <option value="single">موظف واحد</option>
                                <option value="multiple">عدة موظفين</option>
                                <option value="all">جميع الموظفين</option>
                            </select>
                        </div>

                        <div class="form-group" id="singleEmployeeContainer">
                            <label for="employeeId">الموظف: <span class="required">*</span></label>
                            <select id="employeeId" name="employeeId" required>
                                <option value="">اختر الموظف</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                        </div>

                        <div class="form-group" id="multipleEmployeeContainer" style="display: none;">
                            <label for="multipleEmployees">اختر الموظفين: <span class="required">*</span></label>
                            <select id="multipleEmployees" name="multipleEmployees" multiple style="height: 150px;">
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                            <small>اضغط Ctrl أو Shift للاختيار المتعدد</small>
                        </div>

                        <div class="form-group" id="departmentFilterContainer" style="display: none;">
                            <label for="departmentFilter">تصفية حسب القسم:</label>
                            <select id="departmentFilter" name="departmentFilter">
                                <option value="">جميع الأقسام</option>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="thanksType">نوع التشكر: <span class="required">*</span></label>
                            <select id="thanksType" name="thanksType" required>
                                <option value="">اختر نوع التشكر</option>
                                <option value="appreciation">كتاب شكر وتقدير (شهر واحد)</option>
                                <option value="ministerial">كتاب شكر وزاري (شهر واحد)</option>
                                <option value="presidential">كتاب شكر رئاسي (6 أشهر)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="thanksDate">تاريخ التشكر: <span class="required">*</span></label>
                            <input type="date" id="thanksDate" name="thanksDate" required>
                        </div>
                        <div class="form-group">
                            <label for="thanksNumber">رقم الكتاب:</label>
                            <input type="text" id="thanksNumber" name="thanksNumber">
                        </div>
                        <div class="form-group">
                            <label for="thanksIssuer">الجهة المانحة: <span class="required">*</span></label>
                            <input type="text" id="thanksIssuer" name="thanksIssuer" required>
                        </div>
                        <div class="form-group">
                            <label for="thanksReason">سبب التشكر:</label>
                            <textarea id="thanksReason" name="thanksReason" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="thanksEffect">التأثير على الترفيع والعلاوة:</label>
                            <select id="thanksEffect" name="thanksEffect">
                                <option value="none">لا يوجد</option>
                                <option value="1month_university">رئيس الجامعة - تقديم شهر واحد</option>
                                <option value="1month_minister">وزير التعليم العالي - تقديم شهر واحد</option>
                                <option value="6months_pm">رئيس الوزراء - تقديم 6 أشهر</option>
                            </select>
                            <small class="form-text text-muted">* إذا أتى كتاب الشكر بين فترة علاوتين، يتم تقديم العلاوة شهر واحد من آخر تاريخ.</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                            <button type="reset" class="btn btn-secondary"><i class="fas fa-undo"></i> إعادة تعيين</button>
                        </div>
                    </form>
                </div>

                <div class="thanks-list-section">
                    <div class="list-header">
                        <h2><i class="fas fa-list"></i> قائمة التشكرات</h2>
                        <div class="list-actions">
                            <div class="search-container">
                                <input type="text" id="searchThanks" placeholder="بحث..." class="search-input">
                                <button class="search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <select id="filterThanksType" class="filter-select">
                                <option value="">جميع الأنواع</option>
                                <option value="ministerial">وزاري</option>
                                <option value="presidential">رئاسي</option>
                                <option value="university">جامعي</option>
                            </select>
                            <select id="filterThanksEffect" class="filter-select">
                                <option value="">جميع التأثيرات</option>
                                <option value="none">لا يوجد تأثير</option>
                                <option value="1month_university">رئيس الجامعة - تقديم شهر واحد</option>
                                <option value="1month_minister">وزير التعليم العالي - تقديم شهر واحد</option>
                                <option value="6months_pm">رئيس الوزراء - تقديم 6 أشهر</option>
                            </select>
                        </div>
                    </div>

                    <div class="thanks-list">
                        <table id="thanksTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الموظف</th>
                                    <th>نوع التشكر</th>
                                    <th>تاريخ التشكر</th>
                                    <th>الجهة المانحة</th>
                                    <th>التأثير</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-right"></i></button>
                        <span class="pagination-info">صفحة <span id="currentPage">1</span> من <span id="totalPages">1</span></span>
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <!-- نافذة عرض تفاصيل التشكر -->
    <div class="modal" id="viewThanksModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تفاصيل التشكر</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="thanks-details">
                    <div class="info-group">
                        <label>الموظف:</label>
                        <span id="viewEmployeeName"></span>
                    </div>
                    <div class="info-group">
                        <label>نوع التشكر:</label>
                        <span id="viewThanksType"></span>
                    </div>
                    <div class="info-group">
                        <label>تاريخ التشكر:</label>
                        <span id="viewThanksDate"></span>
                    </div>
                    <div class="info-group">
                        <label>رقم الكتاب:</label>
                        <span id="viewThanksNumber"></span>
                    </div>
                    <div class="info-group">
                        <label>الجهة المانحة:</label>
                        <span id="viewThanksIssuer"></span>
                    </div>
                    <div class="info-group">
                        <label>سبب التشكر:</label>
                        <span id="viewThanksReason"></span>
                    </div>
                    <div class="info-group">
                        <label>التأثير على الترفيع والعلاوة:</label>
                        <span id="viewThanksEffect"></span>
                        <small class="text-muted d-block mt-1">* إذا أتى كتاب الشكر بين فترة علاوتين، يتم تقديم العلاوة شهر واحد من آخر تاريخ.</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="editThanksBtn" class="btn btn-primary"><i class="fas fa-edit"></i> تعديل</button>
                <button id="closeViewBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل التشكر -->
    <div class="modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تعديل التشكر</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editThanksForm" class="thanks-form">
                    <input type="hidden" id="editThanksId">
                    <div class="form-group">
                        <label for="editEmployeeId">الموظف: <span class="required">*</span></label>
                        <select id="editEmployeeId" name="editEmployeeId" required>
                            <option value="">اختر الموظف</option>
                            <!-- سيتم ملء هذا الجزء ديناميكياً -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editThanksType">نوع التشكر: <span class="required">*</span></label>
                        <select id="editThanksType" name="editThanksType" required>
                            <option value="">اختر نوع التشكر</option>
                            <option value="ministerial">وزاري</option>
                            <option value="presidential">رئاسي</option>
                            <option value="university">جامعي</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editThanksDate">تاريخ التشكر: <span class="required">*</span></label>
                        <input type="date" id="editThanksDate" name="editThanksDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editThanksNumber">رقم الكتاب:</label>
                        <input type="text" id="editThanksNumber" name="editThanksNumber">
                    </div>
                    <div class="form-group">
                        <label for="editThanksIssuer">الجهة المانحة: <span class="required">*</span></label>
                        <input type="text" id="editThanksIssuer" name="editThanksIssuer" required>
                    </div>
                    <div class="form-group">
                        <label for="editThanksReason">سبب التشكر:</label>
                        <textarea id="editThanksReason" name="editThanksReason" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editThanksEffect">التأثير على الترفيع والعلاوة:</label>
                        <select id="editThanksEffect" name="editThanksEffect">
                            <option value="none">لا يوجد</option>
                            <option value="1month_university">رئيس الجامعة - تقديم شهر واحد</option>
                            <option value="1month_minister">وزير التعليم العالي - تقديم شهر واحد</option>
                            <option value="6months_pm">رئيس الوزراء - تقديم 6 أشهر</option>
                        </select>
                        <small class="form-text text-muted">* إذا أتى كتاب الشكر بين فترة علاوتين، يتم تقديم العلاوة شهر واحد من آخر تاريخ.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="saveEditBtn" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                <button id="cancelEditBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>تأكيد الحذف</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا التشكر؟</p>
                <p class="warning-text">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button id="confirmDeleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> تأكيد الحذف</button>
                <button id="cancelDeleteBtn" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="simple-messages.js"></script>
    <script src="employees-data.js"></script>
    <script src="thanks-calculator.js"></script>
    <script src="fix-missing-employee.js"></script>
    <script src="thanks-simplified.js"></script>
    <script src="thanks.js"></script>
    <script src="allowance-changes.js"></script>
    <script src="thanks-debug.js"></script>
</body>
</html>
