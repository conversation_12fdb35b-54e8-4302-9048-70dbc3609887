// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة تقارير العلاوات المستحقة
    initAllowanceReportsPage();
});

// تهيئة صفحة تقارير العلاوات المستحقة
function initAllowanceReportsPage() {
    // تحميل بيانات الموظفين المستحقين للعلاوة
    loadEligibleEmployees();

    // تهيئة فلاتر البحث
    initFilters();

    // تهيئة أزرار التصدير والطباعة
    initExportAndPrint();
}

// تحميل بيانات الموظفين المستحقين للعلاوة
function loadEligibleEmployees() {
    // الحصول على بيانات الموظفين من التخزين المحلي
    let employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // إذا لم توجد بيانات، إنشاء بيانات تجريبية
    if (employees.length === 0) {
        employees = createSampleEmployeesForReports();
        localStorage.setItem('employees', JSON.stringify(employees));
    }

    // الحصول على التاريخ الحالي
    const today = new Date();

    // تحديد فترة التنبيه (افتراضياً 90 يوم لإظهار المزيد من البيانات)
    const alertPeriod = 90;

    // تصفية الموظفين المستحقين للعلاوة خلال فترة التنبيه
    const eligibleEmployees = employees.filter(employee => {
        if (!employee.nextAllowanceDate && !employee.currentDueDate) return false;

        // استخدام currentDueDate إذا لم يوجد nextAllowanceDate
        const dueDate = employee.nextAllowanceDate || employee.currentDueDate;
        if (!dueDate) return false;

        const nextAllowanceDate = new Date(dueDate);
        const diffTime = nextAllowanceDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays <= alertPeriod && diffDays >= -30; // تضمين المتأخرين أيض<|im_start|>
    });

    // عرض الموظفين المستحقين للعلاوة
    displayEligibleEmployees(eligibleEmployees);

    // تحديث عدد الموظفين المستحقين
    document.getElementById('totalEmployees').textContent = eligibleEmployees.length;
}

// عرض الموظفين المستحقين للعلاوة
function displayEligibleEmployees(employees) {
    const tableBody = document.querySelector('.table-enhanced tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم يكن هناك موظفين مستحقين، عرض رسالة
    if (employees.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="9" class="text-center">لا يوجد موظفين مستحقين للعلاوة خلال فترة التنبيه.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // ترتيب الموظفين حسب تاريخ الاستحقاق (الأقرب أولاً)
    employees.sort((a, b) => new Date(a.nextAllowanceDate) - new Date(b.nextAllowanceDate));

    // إضافة الموظفين إلى الجدول
    employees.forEach((employee, index) => {
        const row = document.createElement('tr');

        // تحويل الوصف الوظيفي إلى نص عربي
        let jobDescriptionText = '';
        switch (employee.jobDescription) {
            case 'teaching':
                jobDescriptionText = 'تدريسي';
                break;
            case 'technical':
                jobDescriptionText = 'فني';
                break;
            case 'administrative':
                jobDescriptionText = 'اداري';
                break;
            default:
                jobDescriptionText = employee.jobDescription;
        }

        // التحقق مما إذا كان تاريخ العلاوة قد تم تقديمه بسبب كتب الشكر
        const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        const employeeThanks = thanks.filter(thank => thank.employeeId == employee.id);

        // البحث عن التشكرات التي تؤثر على العلاوة
        const allowanceAffectingThanks = employeeThanks.filter(thank => {
            const thankDate = new Date(thank.date);
            const lastAllowanceDate = new Date(employee.lastAllowanceDate);
            const nextAllowanceDate = new Date(employee.nextAllowanceDate);

            return thank.effect !== 'none' && thankDate > lastAllowanceDate && thankDate < nextAllowanceDate;
        });

        // إضافة علامة إذا كان تاريخ العلاوة قد تم تقديمه
        let nextAllowanceDate = formatDate(employee.nextAllowanceDate);
        if (allowanceAffectingThanks.length > 0) {
            nextAllowanceDate += ' <i class="fas fa-star text-warning" title="تم تقديم العلاوة بسبب كتب الشكر"></i>';
        }

        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${employee.name}</td>
            <td>${jobDescriptionText}</td>
            <td>${employee.jobTitle || '-'}</td>
            <td>${employee.workLocation || '-'}</td>
            <td>${employee.currentGrade || '-'}</td>
            <td>${employee.currentStage || '-'}</td>
            <td>${nextAllowanceDate}</td>
            <td>
                <button class="btn-enhanced btn-primary-enhanced view-details-btn" data-id="${employee.id}" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn-enhanced btn-secondary-enhanced print-btn" data-id="${employee.id}" style="padding: 0.4rem 0.8rem; font-size: 0.9rem;">
                    <i class="fas fa-print"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار الإجراءات
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار الإجراءات
function addActionButtonsEventListeners() {
    // أزرار عرض التفاصيل
    const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
    viewDetailsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            window.location.href = `employee-details.html?id=${employeeId}`;
        });
    });

    // أزرار الطباعة
    const printButtons = document.querySelectorAll('.print-btn');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.getAttribute('data-id');
            printEmployeeAllowanceReport(employeeId);
        });
    });
}

// تهيئة فلاتر البحث
function initFilters() {
    const searchInput = document.querySelector('.search-container input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            applyFilters();
        });

        // إضافة placeholder واضح
        searchInput.placeholder = 'البحث بالاسم، الوظيفة، أو موقع العمل...';

        console.log('تم تهيئة البحث في التقارير');
    } else {
        console.error('لم يتم العثور على حقل البحث');
    }
}

// تطبيق الفلاتر
function applyFilters() {
    const searchInput = document.querySelector('.search-container input');
    if (!searchInput) return;

    const searchTerm = searchInput.value.trim().toLowerCase();

    // الحصول على الموظفين
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // الحصول على التاريخ الحالي
    const today = new Date();

    // تحديد فترة التنبيه (افتراضياً 90 يوم لإظهار المزيد من البيانات)
    const alertPeriod = 90;

    // تصفية الموظفين المستحقين للعلاوة خلال فترة التنبيه
    let eligibleEmployees = employees.filter(employee => {
        if (!employee.nextAllowanceDate && !employee.currentDueDate) return false;

        // استخدام currentDueDate إذا لم يوجد nextAllowanceDate
        const dueDate = employee.nextAllowanceDate || employee.currentDueDate;
        if (!dueDate) return false;

        const nextAllowanceDate = new Date(dueDate);
        const diffTime = nextAllowanceDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays <= alertPeriod && diffDays >= -30; // تضمين المتأخرين أيض<|im_start|>
    });

    // تصفية الموظفين بناءً على مصطلح البحث
    if (searchTerm) {
        console.log('البحث عن:', searchTerm);
        console.log('عدد الموظفين قبل البحث:', eligibleEmployees.length);

        eligibleEmployees = eligibleEmployees.filter(employee => {
            const nameMatch = employee.name.toLowerCase().includes(searchTerm);
            const idMatch = employee.id.toString().includes(searchTerm);
            const jobMatch = employee.jobTitle && employee.jobTitle.toLowerCase().includes(searchTerm);
            const locationMatch = employee.workLocation && employee.workLocation.toLowerCase().includes(searchTerm);

            return nameMatch || idMatch || jobMatch || locationMatch;
        });

        console.log('عدد الموظفين بعد البحث:', eligibleEmployees.length);
    }

    // عرض النتائج المصفاة
    displayEligibleEmployees(eligibleEmployees);

    // تحديث عدد الموظفين المستحقين
    document.getElementById('totalEmployees').textContent = eligibleEmployees.length;
}

// تهيئة أزرار التصدير والطباعة
function initExportAndPrint() {
    const exportBtn = document.getElementById('exportBtn');
    const printBtn = document.getElementById('printBtn');

    if (exportBtn) {
        exportBtn.addEventListener('click', exportAllowanceReport);
    }

    if (printBtn) {
        printBtn.addEventListener('click', printAllowanceReport);
    }
}

// تصدير تقرير العلاوات
function exportAllowanceReport() {
    alert('سيتم تنفيذ وظيفة تصدير تقرير العلاوات في الإصدار النهائي.');
}

// طباعة تقرير العلاوات
function printAllowanceReport() {
    window.print();
}

// طباعة تقرير علاوة لموظف محدد
function printEmployeeAllowanceReport(employeeId) {
    alert(`سيتم طباعة تقرير العلاوة للموظف رقم ${employeeId} في الإصدار النهائي.`);
}

// تنسيق التاريخ (ميلادي فقط - إجباري)
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    // إجبار استخدام التقويم الميلادي مع تنسيق يدوي لضمان عدم ظهور التاريخ الهجري
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
}

// إنشاء بيانات تجريبية للموظفين للتقارير
function createSampleEmployeesForReports() {
    const today = new Date();
    const sampleEmployees = [];

    // إنشاء 15 موظف تجريبي مع تواريخ مختلفة
    const names = [
        'أحمد محمد علي', 'فاطمة أحمد حسن', 'محمد خالد عبدالله', 'سارة علي محمود', 'عبدالله حسن أحمد',
        'مريم محمد خالد', 'خالد عبدالرحمن', 'نور الدين محمد', 'هدى أحمد علي', 'يوسف محمد حسن',
        'زينب خالد أحمد', 'عمر عبدالله محمد', 'ليلى حسن علي', 'حسام الدين أحمد', 'رنا محمد خالد'
    ];

    const jobTitles = [
        'مهندس برمجيات', 'محاسب', 'مدير مبيعات', 'مسؤول موارد بشرية', 'مهندس شبكات',
        'مصمم جرافيك', 'محلل نظم', 'مدير مشروع', 'مطور ويب', 'مدير تسويق',
        'محاسب مالي', 'مهندس مدني', 'طبيب', 'ممرض', 'صيدلي'
    ];

    const workLocations = [
        'المقر الرئيسي', 'الفرع الأول', 'الفرع الثاني', 'الفرع الثالث', 'المكتب الإقليمي'
    ];

    for (let i = 0; i < 15; i++) {
        // تواريخ مختلفة للعلاوات (بعضها قريب، بعضها بعيد)
        const daysToAdd = Math.floor(Math.random() * 120) - 30; // من -30 إلى +90 يوم
        const nextAllowanceDate = new Date(today);
        nextAllowanceDate.setDate(today.getDate() + daysToAdd);

        const lastAllowanceDate = new Date(nextAllowanceDate);
        lastAllowanceDate.setFullYear(lastAllowanceDate.getFullYear() - 1);

        const employee = {
            id: i + 1,
            name: names[i],
            jobTitle: jobTitles[i],
            workLocation: workLocations[i % workLocations.length],
            currentDegree: Math.floor(Math.random() * 10) + 1,
            currentStage: Math.floor(Math.random() * 15) + 1,
            currentSalary: (Math.floor(Math.random() * 500000) + 300000).toString(),
            lastAllowanceDate: lastAllowanceDate.toISOString().split('T')[0],
            nextAllowanceDate: nextAllowanceDate.toISOString().split('T')[0],
            currentDueDate: nextAllowanceDate.toISOString().split('T')[0],
            seniority: Math.floor(Math.random() * 20) + 1,
            birthDate: '1980-01-01',
            hireDate: '2010-01-01'
        };

        sampleEmployees.push(employee);
    }

    return sampleEmployees;
}
