<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التواريخ الميلادية - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .info {
            border-left-color: #3b82f6;
            background: #eff6ff;
        }

        .date-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            border-left: 3px solid #667eea;
        }

        .calendar-type {
            font-weight: bold;
            color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calendar-alt"></i> اختبار التواريخ الميلادية</h1>
            <p>التأكد من أن جميع التواريخ في النظام تستخدم التقويم الميلادي</p>
        </div>

        <!-- اختبار تنسيق التاريخ -->
        <div class="test-section">
            <h2><i class="fas fa-calendar"></i> اختبار تنسيق التاريخ</h2>
            <input type="date" id="testDate" class="date-input" value="2025-01-15">
            <button class="btn" onclick="testDateFormatting()">اختبار التنسيق</button>
            <div id="dateFormatResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار التاريخ الحالي -->
        <div class="test-section">
            <h2><i class="fas fa-clock"></i> التاريخ الحالي</h2>
            <button class="btn" onclick="showCurrentDate()">عرض التاريخ الحالي</button>
            <div id="currentDateResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار مقارنة التقاويم -->
        <div class="test-section">
            <h2><i class="fas fa-balance-scale"></i> مقارنة التقاويم</h2>
            <input type="date" id="compareDate" class="date-input" value="2025-01-15">
            <button class="btn" onclick="compareCalendars()">مقارنة التقاويم</button>
            <div id="compareResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار حقول التاريخ في النظام -->
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> اختبار حقول النظام</h2>
            <button class="btn" onclick="testSystemDateFields()">اختبار حقول التاريخ</button>
            <div id="systemFieldsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // اختبار تنسيق التاريخ
        function testDateFormatting() {
            const dateInput = document.getElementById('testDate');
            const resultDiv = document.getElementById('dateFormatResult');
            
            if (!dateInput.value) {
                resultDiv.innerHTML = '<p style="color: red;">يرجى اختيار تاريخ للاختبار</p>';
                resultDiv.style.display = 'block';
                return;
            }

            const date = new Date(dateInput.value);
            
            // تنسيقات مختلفة
            const formats = {
                'التنسيق الافتراضي': date.toLocaleDateString('ar'),
                'التنسيق الميلادي الصريح': date.toLocaleDateString('ar-SA-u-ca-gregory', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    calendar: 'gregory'
                }),
                'التنسيق المستخدم في النظام': formatDate(dateInput.value),
                'ISO Format': date.toISOString().split('T')[0],
                'التنسيق الإنجليزي': date.toLocaleDateString('en-US')
            };

            let html = '<h4>نتائج تنسيق التاريخ:</h4>';
            Object.entries(formats).forEach(([name, formatted]) => {
                html += `
                    <div class="date-display">
                        <span class="calendar-type">${name}:</span> ${formatted}
                    </div>
                `;
            });

            resultDiv.innerHTML = html;
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        }

        // عرض التاريخ الحالي
        function showCurrentDate() {
            const resultDiv = document.getElementById('currentDateResult');
            const now = new Date();

            const formats = {
                'التاريخ الحالي (ميلادي)': formatDate(now.toISOString().split('T')[0]),
                'التاريخ والوقت الكامل': now.toLocaleString('ar-SA-u-ca-gregory'),
                'ISO Format': now.toISOString(),
                'Unix Timestamp': now.getTime(),
                'السنة الميلادية': now.getFullYear(),
                'الشهر الميلادي': now.getMonth() + 1,
                'اليوم الميلادي': now.getDate()
            };

            let html = '<h4>التاريخ الحالي بتنسيقات مختلفة:</h4>';
            Object.entries(formats).forEach(([name, value]) => {
                html += `
                    <div class="date-display">
                        <span class="calendar-type">${name}:</span> ${value}
                    </div>
                `;
            });

            resultDiv.innerHTML = html;
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
        }

        // مقارنة التقاويم
        function compareCalendars() {
            const dateInput = document.getElementById('compareDate');
            const resultDiv = document.getElementById('compareResult');
            
            if (!dateInput.value) {
                resultDiv.innerHTML = '<p style="color: red;">يرجى اختيار تاريخ للمقارنة</p>';
                resultDiv.style.display = 'block';
                return;
            }

            const date = new Date(dateInput.value);

            // تنسيقات مختلفة للتقاويم
            const calendars = {
                'الميلادي (Gregory)': date.toLocaleDateString('ar-SA-u-ca-gregory'),
                'الهجري (Islamic)': date.toLocaleDateString('ar-SA-u-ca-islamic'),
                'الميلادي الإنجليزي': date.toLocaleDateString('en-US'),
                'الميلادي العربي': date.toLocaleDateString('ar')
            };

            let html = '<h4>مقارنة التقاويم لنفس التاريخ:</h4>';
            html += '<p><strong>ملاحظة:</strong> النظام يستخدم التقويم الميلادي فقط</p>';
            
            Object.entries(calendars).forEach(([name, formatted]) => {
                const isGregorian = name.includes('الميلادي') || name.includes('Gregory');
                const style = isGregorian ? 'border-left-color: #10b981;' : 'border-left-color: #ef4444;';
                html += `
                    <div class="date-display" style="${style}">
                        <span class="calendar-type">${name}:</span> ${formatted}
                        ${isGregorian ? ' ✅' : ' ❌ (غير مستخدم)'}
                    </div>
                `;
            });

            resultDiv.innerHTML = html;
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
        }

        // اختبار حقول التاريخ في النظام
        function testSystemDateFields() {
            const resultDiv = document.getElementById('systemFieldsResult');
            
            // محاكاة اختبار حقول التاريخ
            const dateFields = [
                { name: 'تاريخ الشكر', type: 'date', format: 'YYYY-MM-DD', calendar: 'ميلادي' },
                { name: 'تاريخ التعيين', type: 'date', format: 'YYYY-MM-DD', calendar: 'ميلادي' },
                { name: 'تاريخ الميلاد', type: 'date', format: 'YYYY-MM-DD', calendar: 'ميلادي' },
                { name: 'تاريخ الترفيع', type: 'date', format: 'YYYY-MM-DD', calendar: 'ميلادي' },
                { name: 'تاريخ التقاعد', type: 'date', format: 'YYYY-MM-DD', calendar: 'ميلادي' }
            ];

            let html = '<h4>حقول التاريخ في النظام:</h4>';
            html += '<p><strong>جميع الحقول تستخدم التقويم الميلادي</strong></p>';
            
            dateFields.forEach(field => {
                html += `
                    <div class="date-display">
                        <span class="calendar-type">${field.name}:</span> 
                        النوع: ${field.type} | التنسيق: ${field.format} | التقويم: ${field.calendar} ✅
                    </div>
                `;
            });

            html += `
                <div style="margin-top: 15px; padding: 10px; background: #e7f3ff; border-radius: 5px;">
                    <strong>✅ تأكيد:</strong> جميع حقول التاريخ في النظام تستخدم التقويم الميلادي بتنسيق ISO (YYYY-MM-DD)
                </div>
            `;

            resultDiv.innerHTML = html;
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        }

        // وظيفة تنسيق التاريخ (نفس المستخدمة في النظام)
        function formatDate(dateString) {
            if (!dateString) return '-';

            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            // استخدام التقويم الميلادي بوضوح
            return date.toLocaleDateString('ar-SA-u-ca-gregory', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            });
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('load', function() {
            showCurrentDate();
        });
    </script>
</body>
</html>
