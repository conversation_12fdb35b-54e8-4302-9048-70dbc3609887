// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة إجازات الموظفين
    initEmployeeLeavesPage();
});

// تهيئة صفحة إجازات الموظفين
function initEmployeeLeavesPage() {
    // تحميل الموظفين
    loadEmployees();

    // تحميل أنواع الإجازات
    loadLeaveTypes();

    // تحميل إجازات الموظفين
    loadEmployeeLeaves();

    // تهيئة نموذج إضافة إجازة
    initAddLeaveForm();

    // تهيئة البحث والتصفية
    initSearchAndFilter();

    // تهيئة النوافذ المنبثقة
    initModals();

    // تهيئة حساب عدد أيام الإجازة
    initLeaveDaysCalculation();
}

// تحميل الموظفين
function loadEmployees() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let employees = localStorage.getItem('employees');

    if (!employees) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        employees = [
            { id: 1, name: 'أحمد محمد علي', jobTitle: 'مهندس برمجيات', department: 'تكنولوجيا المعلومات' },
            { id: 2, name: 'فاطمة أحمد حسين', jobTitle: 'محاسب', department: 'المالية' },
            { id: 3, name: 'محمد عبدالله محمود', jobTitle: 'مدير مشروع', department: 'إدارة المشاريع' },
            { id: 4, name: 'زينب علي حسن', jobTitle: 'مسؤول موارد بشرية', department: 'الموارد البشرية' },
            { id: 5, name: 'عمر خالد سعيد', jobTitle: 'مطور ويب', department: 'تكنولوجيا المعلومات' }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('employees', JSON.stringify(employees));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        employees = JSON.parse(employees);
    }

    // ملء قائمة الموظفين في نموذج إضافة الإجازة
    populateEmployeeSelect('employeeId', employees);

    // ملء قائمة الموظفين في نموذج تعديل الإجازة
    populateEmployeeSelect('editEmployeeId', employees);
}

// ملء قائمة الموظفين
function populateEmployeeSelect(selectId, employees) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // الحفاظ على الخيار الافتراضي
    const defaultOption = select.options[0];
    select.innerHTML = '';
    select.appendChild(defaultOption);

    // إضافة الموظفين إلى القائمة
    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;
        option.textContent = employee.name;
        select.appendChild(option);
    });
}

// تحميل أنواع الإجازات
function loadLeaveTypes() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let leaveTypes = localStorage.getItem('leaves');

    if (!leaveTypes) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        leaveTypes = [
            { id: 1, name: 'إجازة سنوية', maxDays: 30, affectsAllowancePromotion: false, description: 'الإجازة السنوية المستحقة للموظف' },
            { id: 2, name: 'إجازة مرضية', maxDays: 15, affectsAllowancePromotion: false, description: 'إجازة مرضية بتقرير طبي' },
            { id: 3, name: 'إجازة بدون راتب', maxDays: 0, affectsAllowancePromotion: true, description: 'إجازة بدون راتب حسب الأيام المحددة - تؤثر على العلاوة والترفيع' },
            { id: 4, name: 'إجازة أمومة', maxDays: 90, affectsAllowancePromotion: false, description: 'إجازة أمومة للموظفات' },
            { id: 5, name: 'إجازة حج', maxDays: 21, affectsAllowancePromotion: false, description: 'إجازة لأداء فريضة الحج' },
            { id: 6, name: 'إجازة خمس سنوات', maxDays: 1825, affectsAllowancePromotion: true, description: 'إجازة لمدة خمس سنوات - تؤثر على العلاوة والترفيع' },
            { id: 7, name: 'إجازة دراسية', maxDays: 365, affectsAllowancePromotion: false, description: 'إجازة لإكمال الدراسة' },
            { id: 8, name: 'إجازة مرضية (عجز صحي)', maxDays: 180, affectsAllowancePromotion: false, description: 'إجازة مرضية لمدة 6 أشهر بسبب عجز صحي' }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('leaves', JSON.stringify(leaveTypes));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        leaveTypes = JSON.parse(leaveTypes);
    }

    // ملء قائمة أنواع الإجازات في نموذج إضافة الإجازة
    populateLeaveTypeSelect('leaveType', leaveTypes);

    // ملء قائمة أنواع الإجازات في نموذج تعديل الإجازة
    populateLeaveTypeSelect('editLeaveType', leaveTypes);

    // ملء قائمة تصفية أنواع الإجازات
    populateLeaveTypeSelect('filterLeaveType', leaveTypes);
}

// ملء قائمة أنواع الإجازات
function populateLeaveTypeSelect(selectId, leaveTypes) {
    const select = document.getElementById(selectId);
    if (!select) return;

    // الحفاظ على الخيار الافتراضي
    const defaultOption = select.options[0];
    select.innerHTML = '';
    select.appendChild(defaultOption);

    // إضافة أنواع الإجازات إلى القائمة
    leaveTypes.forEach(leaveType => {
        const option = document.createElement('option');
        option.value = leaveType.id;
        option.textContent = leaveType.name;
        select.appendChild(option);
    });
}

// تحميل إجازات الموظفين
function loadEmployeeLeaves() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let employeeLeaves = localStorage.getItem('employeeLeaves');

    if (!employeeLeaves) {
        // إذا لم تكن هناك بيانات، استخدم بيانات افتراضية
        const today = new Date();
        const oneDay = 24 * 60 * 60 * 1000; // ميلي ثانية في يوم واحد

        employeeLeaves = [
            {
                id: 1,
                employeeId: 1,
                leaveTypeId: 1,
                startDate: new Date(today.getTime() - 10 * oneDay).toISOString().split('T')[0],
                endDate: new Date(today.getTime() - 5 * oneDay).toISOString().split('T')[0],
                days: 6,
                leaveNumber: 'LV-2023-001',
                notes: 'إجازة سنوية',
                status: 'completed'
            },
            {
                id: 2,
                employeeId: 2,
                leaveTypeId: 4,
                startDate: new Date(today.getTime() - 30 * oneDay).toISOString().split('T')[0],
                endDate: new Date(today.getTime() + 60 * oneDay).toISOString().split('T')[0],
                days: 91,
                leaveNumber: 'LV-2023-002',
                notes: 'إجازة أمومة',
                status: 'active'
            },
            {
                id: 3,
                employeeId: 3,
                leaveTypeId: 2,
                startDate: new Date(today.getTime() + 5 * oneDay).toISOString().split('T')[0],
                endDate: new Date(today.getTime() + 10 * oneDay).toISOString().split('T')[0],
                days: 6,
                leaveNumber: 'LV-2023-003',
                notes: 'إجازة مرضية',
                status: 'upcoming'
            }
        ];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('employeeLeaves', JSON.stringify(employeeLeaves));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        employeeLeaves = JSON.parse(employeeLeaves);
    }

    // عرض البيانات في الجدول
    displayEmployeeLeaves(employeeLeaves);
}

// عرض إجازات الموظفين في الجدول
function displayEmployeeLeaves(employeeLeaves) {
    const tableBody = document.querySelector('#employeeLeavesTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك إجازات، عرض رسالة
    if (employeeLeaves.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="8" class="text-center">لا توجد إجازات. أضف إجازة جديدة.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // الحصول على الموظفين وأنواع الإجازات
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const leaveTypes = JSON.parse(localStorage.getItem('leaves') || '[]');

    // إضافة الصفوف إلى الجدول
    employeeLeaves.forEach((leave, index) => {
        // البحث عن الموظف ونوع الإجازة
        const employee = employees.find(emp => emp.id == leave.employeeId) || { name: 'غير معروف' };
        const leaveType = leaveTypes.find(type => type.id == leave.leaveTypeId) || { name: 'غير معروف' };

        // تحديد حالة الإجازة
        const statusText = getStatusText(leave.status);
        const statusClass = getStatusClass(leave.status);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${employee.name}</td>
            <td>${leaveType.name}</td>
            <td>${formatDate(leave.startDate)}</td>
            <td>${formatDate(leave.endDate)}</td>
            <td>${leave.days} يوم</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view-btn" data-id="${leave.id}" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit-btn" data-id="${leave.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${leave.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار العرض والتعديل والحذف
    addActionButtonsEventListeners();
}

// الحصول على نص حالة الإجازة
function getStatusText(status) {
    switch (status) {
        case 'active': return 'جارية';
        case 'completed': return 'منتهية';
        case 'upcoming': return 'قادمة';
        default: return 'غير معروف';
    }
}

// الحصول على صنف حالة الإجازة
function getStatusClass(status) {
    switch (status) {
        case 'active': return 'status-active';
        case 'completed': return 'status-completed';
        case 'upcoming': return 'status-upcoming';
        default: return '';
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
}

// إضافة مستمعي الأحداث لأزرار العرض والتعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار العرض
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            openViewModal(leaveId);
        });
    });

    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            openEditModal(leaveId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            openDeleteModal(leaveId);
        });
    });
}

// تهيئة نموذج إضافة إجازة
function initAddLeaveForm() {
    const addForm = document.getElementById('addLeaveForm');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على قيم الحقول
        const employeeId = document.getElementById('employeeId').value;
        const leaveTypeId = document.getElementById('leaveType').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const days = document.getElementById('leaveDays').value;
        const leaveNumber = document.getElementById('leaveNumber').value.trim();
        const notes = document.getElementById('leaveNotes').value.trim();

        // التحقق من صحة البيانات
        if (!employeeId) {
            alert('يرجى اختيار الموظف');
            return;
        }

        if (!leaveTypeId) {
            alert('يرجى اختيار نوع الإجازة');
            return;
        }

        if (!startDate) {
            alert('يرجى تحديد تاريخ بداية الإجازة');
            return;
        }

        if (!endDate) {
            alert('يرجى تحديد تاريخ نهاية الإجازة');
            return;
        }

        // التحقق من أن تاريخ البداية قبل تاريخ النهاية
        if (new Date(startDate) > new Date(endDate)) {
            alert('يجب أن يكون تاريخ البداية قبل تاريخ النهاية');
            return;
        }

        // إضافة الإجازة الجديدة
        addEmployeeLeave(employeeId, leaveTypeId, startDate, endDate, days, leaveNumber, notes);

        // إعادة تعيين النموذج
        this.reset();
    });
}

// إضافة إجازة جديدة
function addEmployeeLeave(employeeId, leaveTypeId, startDate, endDate, days, leaveNumber, notes) {
    // الحصول على إجازات الموظفين الحالية
    let employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');

    // إنشاء معرف فريد جديد
    const newId = employeeLeaves.length > 0 ? Math.max(...employeeLeaves.map(leave => leave.id)) + 1 : 1;

    // تحديد حالة الإجازة
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    let status;
    if (end < today) {
        status = 'completed';
    } else if (start > today) {
        status = 'upcoming';
    } else {
        status = 'active';
    }

    // إنشاء الإجازة الجديدة
    const newLeave = {
        id: newId,
        employeeId: parseInt(employeeId),
        leaveTypeId: parseInt(leaveTypeId),
        startDate: startDate,
        endDate: endDate,
        days: parseInt(days),
        leaveNumber: leaveNumber,
        notes: notes,
        status: status
    };

    // إضافة الإجازة الجديدة إلى المصفوفة
    employeeLeaves.push(newLeave);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('employeeLeaves', JSON.stringify(employeeLeaves));

    // تحديث عرض الجدول
    displayEmployeeLeaves(employeeLeaves);

    // عرض رسالة نجاح
    showNotification('تم إضافة الإجازة بنجاح', 'success');
}

// تهيئة حساب عدد أيام الإجازة
function initLeaveDaysCalculation() {
    // حساب عدد أيام الإجازة في نموذج الإضافة
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const leaveDaysInput = document.getElementById('leaveDays');

    if (startDateInput && endDateInput && leaveDaysInput) {
        startDateInput.addEventListener('change', calculateLeaveDays);
        endDateInput.addEventListener('change', calculateLeaveDays);
    }

    // حساب عدد أيام الإجازة في نموذج التعديل
    const editStartDateInput = document.getElementById('editStartDate');
    const editEndDateInput = document.getElementById('editEndDate');
    const editLeaveDaysInput = document.getElementById('editLeaveDays');

    if (editStartDateInput && editEndDateInput && editLeaveDaysInput) {
        editStartDateInput.addEventListener('change', calculateEditLeaveDays);
        editEndDateInput.addEventListener('change', calculateEditLeaveDays);
    }
}

// حساب عدد أيام الإجازة في نموذج الإضافة
function calculateLeaveDays() {
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const leaveDaysInput = document.getElementById('leaveDays');

    if (!startDateInput.value || !endDateInput.value) return;

    const startDate = new Date(startDateInput.value);
    const endDate = new Date(endDateInput.value);

    // التحقق من أن تاريخ البداية قبل تاريخ النهاية
    if (startDate > endDate) {
        alert('يجب أن يكون تاريخ البداية قبل تاريخ النهاية');
        endDateInput.value = '';
        leaveDaysInput.value = '';
        return;
    }

    // حساب عدد الأيام (بما في ذلك يوم البداية ويوم النهاية)
    const oneDay = 24 * 60 * 60 * 1000; // ميلي ثانية في يوم واحد
    const days = Math.round((endDate - startDate) / oneDay) + 1;

    leaveDaysInput.value = days;
}

// حساب عدد أيام الإجازة في نموذج التعديل
function calculateEditLeaveDays() {
    const startDateInput = document.getElementById('editStartDate');
    const endDateInput = document.getElementById('editEndDate');
    const leaveDaysInput = document.getElementById('editLeaveDays');

    if (!startDateInput.value || !endDateInput.value) return;

    const startDate = new Date(startDateInput.value);
    const endDate = new Date(endDateInput.value);

    // التحقق من أن تاريخ البداية قبل تاريخ النهاية
    if (startDate > endDate) {
        alert('يجب أن يكون تاريخ البداية قبل تاريخ النهاية');
        endDateInput.value = '';
        leaveDaysInput.value = '';
        return;
    }

    // حساب عدد الأيام (بما في ذلك يوم البداية ويوم النهاية)
    const oneDay = 24 * 60 * 60 * 1000; // ميلي ثانية في يوم واحد
    const days = Math.round((endDate - startDate) / oneDay) + 1;

    leaveDaysInput.value = days;
}

// تهيئة البحث والتصفية
function initSearchAndFilter() {
    // البحث
    const searchInput = document.getElementById('searchLeave');
    if (searchInput) {
        searchInput.addEventListener('input', filterEmployeeLeaves);
    }

    // تصفية نوع الإجازة
    const filterLeaveType = document.getElementById('filterLeaveType');
    if (filterLeaveType) {
        filterLeaveType.addEventListener('change', filterEmployeeLeaves);
    }

    // تصفية حالة الإجازة
    const filterLeaveStatus = document.getElementById('filterLeaveStatus');
    if (filterLeaveStatus) {
        filterLeaveStatus.addEventListener('change', filterEmployeeLeaves);
    }
}

// تصفية إجازات الموظفين
function filterEmployeeLeaves() {
    const searchTerm = document.getElementById('searchLeave').value.trim().toLowerCase();
    const leaveTypeFilter = document.getElementById('filterLeaveType').value;
    const leaveStatusFilter = document.getElementById('filterLeaveStatus').value;

    // الحصول على إجازات الموظفين
    const employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');

    // الحصول على الموظفين وأنواع الإجازات
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const leaveTypes = JSON.parse(localStorage.getItem('leaves') || '[]');

    // تصفية إجازات الموظفين
    const filteredLeaves = employeeLeaves.filter(leave => {
        // البحث عن الموظف ونوع الإجازة
        const employee = employees.find(emp => emp.id == leave.employeeId) || { name: 'غير معروف' };
        const leaveType = leaveTypes.find(type => type.id == leave.leaveTypeId) || { name: 'غير معروف' };

        // تصفية حسب مصطلح البحث
        const matchesSearch = searchTerm === '' ||
            employee.name.toLowerCase().includes(searchTerm) ||
            leaveType.name.toLowerCase().includes(searchTerm) ||
            (leave.leaveNumber && leave.leaveNumber.toLowerCase().includes(searchTerm)) ||
            (leave.notes && leave.notes.toLowerCase().includes(searchTerm));

        // تصفية حسب نوع الإجازة
        const matchesLeaveType = leaveTypeFilter === '' || leave.leaveTypeId == leaveTypeFilter;

        // تصفية حسب حالة الإجازة
        const matchesLeaveStatus = leaveStatusFilter === '' || leave.status === leaveStatusFilter;

        return matchesSearch && matchesLeaveType && matchesLeaveStatus;
    });

    // عرض النتائج المصفاة
    displayEmployeeLeaves(filteredLeaves);
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const viewModal = document.getElementById('viewLeaveModal');
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إغلاق نافذة العرض
    const closeViewBtn = document.getElementById('closeViewBtn');
    if (closeViewBtn) {
        closeViewBtn.addEventListener('click', function() {
            closeModal(viewModal);
        });
    }

    // زر التعديل في نافذة العرض
    const editLeaveBtn = document.getElementById('editLeaveBtn');
    if (editLeaveBtn) {
        editLeaveBtn.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            closeModal(viewModal);
            openEditModal(leaveId);
        });
    }

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedLeave();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const leaveId = this.getAttribute('data-id');
            deleteEmployeeLeave(leaveId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة عرض تفاصيل الإجازة
function openViewModal(leaveId) {
    // الحصول على الإجازة المراد عرضها
    const employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');
    const leave = employeeLeaves.find(leave => leave.id == leaveId);

    if (!leave) return;

    // الحصول على الموظف ونوع الإجازة
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const leaveTypes = JSON.parse(localStorage.getItem('leaves') || '[]');

    const employee = employees.find(emp => emp.id == leave.employeeId) || { name: 'غير معروف' };
    const leaveType = leaveTypes.find(type => type.id == leave.leaveTypeId) || { name: 'غير معروف' };

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('viewEmployeeName').textContent = employee.name;
    document.getElementById('viewLeaveType').textContent = leaveType.name;
    document.getElementById('viewStartDate').textContent = formatDate(leave.startDate);
    document.getElementById('viewEndDate').textContent = formatDate(leave.endDate);
    document.getElementById('viewLeaveDays').textContent = leave.days + ' يوم';
    document.getElementById('viewLeaveNumber').textContent = leave.leaveNumber || '-';
    document.getElementById('viewLeaveNotes').textContent = leave.notes || '-';
    document.getElementById('viewLeaveStatus').textContent = getStatusText(leave.status);

    // تعيين معرف الإجازة لزر التعديل
    document.getElementById('editLeaveBtn').setAttribute('data-id', leave.id);

    // فتح النافذة
    const viewModal = document.getElementById('viewLeaveModal');
    openModal(viewModal);
}

// فتح نافذة تعديل الإجازة
function openEditModal(leaveId) {
    // الحصول على الإجازة المراد تعديلها
    const employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');
    const leave = employeeLeaves.find(leave => leave.id == leaveId);

    if (!leave) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editLeaveId').value = leave.id;
    document.getElementById('editEmployeeId').value = leave.employeeId;
    document.getElementById('editLeaveType').value = leave.leaveTypeId;
    document.getElementById('editStartDate').value = leave.startDate;
    document.getElementById('editEndDate').value = leave.endDate;
    document.getElementById('editLeaveDays').value = leave.days;
    document.getElementById('editLeaveNumber').value = leave.leaveNumber || '';
    document.getElementById('editLeaveNotes').value = leave.notes || '';

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(leaveId) {
    // تعيين معرف الإجازة لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', leaveId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ الإجازة المعدلة
function saveEditedLeave() {
    // الحصول على قيم الحقول
    const id = document.getElementById('editLeaveId').value;
    const employeeId = document.getElementById('editEmployeeId').value;
    const leaveTypeId = document.getElementById('editLeaveType').value;
    const startDate = document.getElementById('editStartDate').value;
    const endDate = document.getElementById('editEndDate').value;
    const days = document.getElementById('editLeaveDays').value;
    const leaveNumber = document.getElementById('editLeaveNumber').value.trim();
    const notes = document.getElementById('editLeaveNotes').value.trim();

    // التحقق من صحة البيانات
    if (!employeeId) {
        alert('يرجى اختيار الموظف');
        return;
    }

    if (!leaveTypeId) {
        alert('يرجى اختيار نوع الإجازة');
        return;
    }

    if (!startDate) {
        alert('يرجى تحديد تاريخ بداية الإجازة');
        return;
    }

    if (!endDate) {
        alert('يرجى تحديد تاريخ نهاية الإجازة');
        return;
    }

    // التحقق من أن تاريخ البداية قبل تاريخ النهاية
    if (new Date(startDate) > new Date(endDate)) {
        alert('يجب أن يكون تاريخ البداية قبل تاريخ النهاية');
        return;
    }

    // تحديد حالة الإجازة
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    let status;
    if (end < today) {
        status = 'completed';
    } else if (start > today) {
        status = 'upcoming';
    } else {
        status = 'active';
    }

    // الحصول على إجازات الموظفين الحالية
    let employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');

    // البحث عن الإجازة وتحديثها
    const index = employeeLeaves.findIndex(leave => leave.id == id);
    if (index !== -1) {
        employeeLeaves[index] = {
            id: parseInt(id),
            employeeId: parseInt(employeeId),
            leaveTypeId: parseInt(leaveTypeId),
            startDate: startDate,
            endDate: endDate,
            days: parseInt(days),
            leaveNumber: leaveNumber,
            notes: notes,
            status: status
        };

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('employeeLeaves', JSON.stringify(employeeLeaves));

        // تحديث عرض الجدول
        displayEmployeeLeaves(employeeLeaves);

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));

        // عرض رسالة نجاح
        showNotification('تم تحديث الإجازة بنجاح', 'success');
    }
}

// حذف الإجازة
function deleteEmployeeLeave(leaveId) {
    // الحصول على إجازات الموظفين الحالية
    let employeeLeaves = JSON.parse(localStorage.getItem('employeeLeaves') || '[]');

    // حذف الإجازة
    employeeLeaves = employeeLeaves.filter(leave => leave.id != leaveId);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('employeeLeaves', JSON.stringify(employeeLeaves));

    // تحديث عرض الجدول
    displayEmployeeLeaves(employeeLeaves);

    // عرض رسالة نجاح
    showNotification('تم حذف الإجازة بنجاح', 'success');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
