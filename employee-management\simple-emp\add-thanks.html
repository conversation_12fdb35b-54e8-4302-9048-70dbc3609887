<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة شكر وتقدير - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <!-- إضافة خط Cairo من Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- إضافة Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- إضافة أنماط شريط التنقل المحسن -->
    <link rel="stylesheet" href="navbar.css">
    <!-- إضافة أنماط محسنة -->
    <link rel="stylesheet" href="enhanced-styles.css">
    <!-- إضافة أنماط النوافذ المنبثقة -->
    <link rel="stylesheet" href="modals.css">
    <!-- إضافة أنماط الإشعارات -->
    <link rel="stylesheet" href="notifications.css">

    <style>
        .add-thanks-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .page-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .thanks-form-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group-enhanced {
            margin-bottom: 1.5rem;
        }

        .form-label-enhanced {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
            font-size: 1rem;
        }

        .form-control-enhanced, .form-select-enhanced {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control-enhanced:focus, .form-select-enhanced:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .employee-search {
            position: relative;
        }

        .employee-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 10px 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .employee-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s;
        }

        .employee-option:hover {
            background-color: #f8fafc;
        }

        .employee-option:last-child {
            border-bottom: none;
        }

        .selected-employee {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .thanks-preview {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .thanks-preview h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .impact-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem;
            border-radius: 5px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .impact-item:last-child {
            margin-bottom: 0;
        }

        .thanks-scope-cards, .thanks-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .scope-card {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .scope-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .scope-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .scope-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .scope-card h4 {
            margin: 0.5rem 0;
            font-size: 1.1rem;
        }

        .scope-card p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .thanks-type-card {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .thanks-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .thanks-type-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .thanks-type-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .thanks-type-card h4 {
            margin: 0.5rem 0;
            font-size: 1.1rem;
        }

        .thanks-type-card p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        .btn-enhanced {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary-enhanced {
            background: #6b7280;
            color: white;
        }

        .btn-secondary-enhanced:hover {
            background: #4b5563;
        }

        .recent-thanks {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .recent-thanks h3 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .thanks-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .thanks-item {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 10px;
            border-right: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .thanks-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .thanks-item h4 {
            margin: 0 0 0.5rem 0;
            color: #374151;
        }

        .thanks-item p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .form-validation {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
        }

        .success-animation {
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .search-thanks {
            margin-bottom: 1rem;
        }

        .existing-thank-item {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 10px;
            border-right: 4px solid #667eea;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .existing-thank-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .thank-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .thank-employee-name {
            font-weight: 600;
            color: #374151;
            font-size: 1.1rem;
        }

        .thank-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-edit, .btn-delete {
            padding: 0.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #3b82f6;
            color: white;
        }

        .btn-edit:hover {
            background: #2563eb;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background: #dc2626;
        }

        .thank-details {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .thank-type {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .type-appreciation {
            background: #dbeafe;
            color: #1e40af;
        }

        .type-ministerial {
            background: #dcfce7;
            color: #166534;
        }

        .type-presidential {
            background: #fef3c7;
            color: #92400e;
        }

        .selected-employees {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .selected-employees h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .employee-chip {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            margin: 0.25rem;
            font-size: 0.9rem;
        }

        .employee-count {
            background: rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .thanks-type-cards {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                <span>برنامج إدارة العلاوات والترفيعات</span>
            </div>
            <div class="nav-links">
                <div class="dropdown">
                    <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-id-card"></i> الملفات التعريفية <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="work-locations.html"><i class="fas fa-building"></i> مواقع العمل</a>
                        <a href="job-titles.html"><i class="fas fa-user-tie"></i> العناوين الوظيفية</a>
                        <a href="education-levels.html"><i class="fas fa-graduation-cap"></i> التحصيل الدراسي</a>
                        <a href="leaves.html"><i class="fas fa-calendar-alt"></i> الاجازات</a>
                        <a href="alert-periods.html"><i class="fas fa-bell"></i> فترة التنبيه</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-users"></i> الموظفين <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-form.html"><i class="fas fa-user-plus"></i> فتح ملف موظف</a>
                        <a href="employees-list.html"><i class="fas fa-list"></i> عرض الموظفين</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle active"><i class="fas fa-medal"></i> التشكرات والعقوبات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="add-thanks.html" class="active"><i class="fas fa-award"></i> إضافة شكر وتقدير</a>
                        <a href="penalties.html"><i class="fas fa-exclamation-triangle"></i> العقوبات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-calendar-alt"></i> الاجازات والغيابات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="employee-leaves.html"><i class="fas fa-calendar-check"></i> الاجازات</a>
                        <a href="absences.html"><i class="fas fa-calendar-times"></i> الغيابات</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-chart-bar"></i> التقارير <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-reports.html"><i class="fas fa-file-alt"></i> تقرير العلاوات المستحقة</a>
                        <a href="promotion-reports.html"><i class="fas fa-file-alt"></i> تقرير الترفيعات المستحقة</a>
                        <a href="retirement-reports.html"><i class="fas fa-user-clock"></i> قرب الاحالة الى التقاعد</a>
                        <a href="salary-scale.html"><i class="fas fa-money-bill-wave"></i> سلم الرواتب</a>
                    </div>
                </div>

                <div class="dropdown">
                    <a href="#" class="dropdown-toggle"><i class="fas fa-bell"></i> التنبيهات <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="allowance-alerts.html"><i class="fas fa-bell"></i> تنبيه العلاوات المستحقة</a>
                        <a href="promotion-alerts.html"><i class="fas fa-bell"></i> تنبيه الترفيعات المستحقة</a>
                        <a href="retirement-alerts.html"><i class="fas fa-bell"></i> تنبيه الاحالة الى التقاعد</a>
                    </div>
                </div>
            </div>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <main>
        <div class="add-thanks-container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <h1><i class="fas fa-award"></i> إضافة شكر وتقدير</h1>
                <p>إضافة كتب الشكر والتقدير للموظفين مع التحديث التلقائي للعلاوات والترفيعات</p>
            </div>

            <div class="form-grid">
                <!-- نموذج إضافة الشكر -->
                <div class="thanks-form-card">
                    <form id="addThanksForm">
                        <!-- نوع الشكر (فردي أم جماعي) -->
                        <div class="form-section">
                            <h3><i class="fas fa-users"></i> نطاق الشكر</h3>
                            <div class="thanks-scope-cards">
                                <div class="scope-card" data-scope="individual">
                                    <div class="icon"><i class="fas fa-user"></i></div>
                                    <h4>شكر فردي</h4>
                                    <p>موظف واحد محدد</p>
                                </div>
                                <div class="scope-card" data-scope="group">
                                    <div class="icon"><i class="fas fa-users"></i></div>
                                    <h4>شكر جماعي</h4>
                                    <p>مجموعة من الموظفين</p>
                                </div>
                                <div class="scope-card" data-scope="all">
                                    <div class="icon"><i class="fas fa-building"></i></div>
                                    <h4>شكر عام</h4>
                                    <p>جميع الموظفين</p>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار الموظف (للشكر الفردي) -->
                        <div class="form-section" id="individualSection" style="display: none;">
                            <h3><i class="fas fa-user"></i> اختيار الموظف</h3>
                            <div class="form-group-enhanced">
                                <label for="employeeSearch" class="form-label-enhanced">البحث عن الموظف:</label>
                                <div class="employee-search">
                                    <input type="text" id="employeeSearch" class="form-control-enhanced"
                                           placeholder="ابحث بالاسم أو الرقم الوظيفي..." autocomplete="off">
                                    <div id="employeeDropdown" class="employee-dropdown"></div>
                                </div>
                            </div>
                            <div id="selectedEmployee" class="selected-employee">
                                <i class="fas fa-user-check"></i>
                                <span id="selectedEmployeeName"></span>
                            </div>
                        </div>

                        <!-- اختيار المجموعة (للشكر الجماعي) -->
                        <div class="form-section" id="groupSection" style="display: none;">
                            <h3><i class="fas fa-filter"></i> تحديد المجموعة</h3>
                            <div class="form-grid">
                                <div class="form-group-enhanced">
                                    <label for="groupType" class="form-label-enhanced">نوع التصنيف:</label>
                                    <select id="groupType" class="form-select-enhanced">
                                        <option value="">اختر نوع التصنيف</option>
                                        <option value="gender">حسب الجنس</option>
                                        <option value="jobTitle">حسب العنوان الوظيفي</option>
                                        <option value="workLocation">حسب موقع العمل</option>
                                        <option value="educationLevel">حسب المستوى التعليمي</option>
                                        <option value="custom">اختيار مخصص</option>
                                    </select>
                                </div>
                                <div class="form-group-enhanced" id="groupValueContainer" style="display: none;">
                                    <label for="groupValue" class="form-label-enhanced">القيمة:</label>
                                    <select id="groupValue" class="form-select-enhanced">
                                        <option value="">اختر القيمة</option>
                                    </select>
                                </div>
                            </div>
                            <div id="selectedEmployees" class="selected-employees" style="display: none;">
                                <h4><i class="fas fa-users"></i> الموظفين المختارين:</h4>
                                <div id="selectedEmployeesList"></div>
                            </div>
                        </div>

                        <!-- معاينة تأثير الشكر -->
                        <div id="thanksPreview" class="thanks-preview" style="display: none;">
                            <h4><i class="fas fa-info-circle"></i> تأثير الشكر</h4>
                            <div id="thanksImpact"></div>
                        </div>

                        <!-- نوع الشكر -->
                        <div class="form-section">
                            <h3><i class="fas fa-medal"></i> نوع الشكر والتقدير</h3>
                            <div class="thanks-type-cards">
                                <div class="thanks-type-card" data-type="appreciation">
                                    <div class="icon"><i class="fas fa-university"></i></div>
                                    <h4>شكر جامعي</h4>
                                    <p>شهر واحد قدم</p>
                                </div>
                                <div class="thanks-type-card" data-type="ministerial">
                                    <div class="icon"><i class="fas fa-building"></i></div>
                                    <h4>شكر وزاري</h4>
                                    <p>شهر واحد قدم</p>
                                </div>
                                <div class="thanks-type-card" data-type="presidential">
                                    <div class="icon"><i class="fas fa-crown"></i></div>
                                    <h4>شكر رئاسي</h4>
                                    <p>ستة أشهر قدم</p>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل الشكر -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> تفاصيل الشكر</h3>
                            <div class="form-grid">
                                <div class="form-group-enhanced">
                                    <label for="thanksDate" class="form-label-enhanced">تاريخ الشكر:</label>
                                    <input type="date" id="thanksDate" class="form-control-enhanced" required>
                                </div>
                                <div class="form-group-enhanced">
                                    <label for="thanksNumber" class="form-label-enhanced">رقم الكتاب:</label>
                                    <input type="text" id="thanksNumber" class="form-control-enhanced"
                                           placeholder="رقم كتاب الشكر">
                                </div>
                                <div class="form-group-enhanced">
                                    <label for="thanksIssuer" class="form-label-enhanced">الجهة المانحة:</label>
                                    <input type="text" id="thanksIssuer" class="form-control-enhanced"
                                           placeholder="الجهة التي أصدرت الشكر" readonly>
                                </div>
                                <div class="form-group-enhanced">
                                    <label for="thanksReason" class="form-label-enhanced">سبب الشكر:</label>
                                    <input type="text" id="thanksReason" class="form-control-enhanced"
                                           placeholder="سبب منح الشكر والتقدير">
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="action-buttons">
                            <button type="submit" class="btn-enhanced btn-primary-enhanced">
                                <i class="fas fa-save"></i>
                                حفظ الشكر
                            </button>
                            <button type="button" class="btn-enhanced btn-secondary-enhanced" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </form>
                </div>

                <!-- إدارة التشكرات الموجودة -->
                <div class="recent-thanks">
                    <h3><i class="fas fa-list"></i> التشكرات الموجودة</h3>
                    <div class="search-thanks">
                        <input type="text" id="searchExistingThanks" class="form-control-enhanced"
                               placeholder="ابحث في التشكرات الموجودة...">
                    </div>
                    <div id="existingThanksList" class="thanks-list">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>

                <!-- معلومات مفيدة -->
                <div class="recent-thanks" style="margin-top: 2rem;">
                    <h3><i class="fas fa-info-circle"></i> معلومات مهمة</h3>
                    <div class="thanks-list">
                        <div class="thanks-item">
                            <h4>أنواع التشكرات وتأثيرها:</h4>
                            <p><strong>شكر جامعي:</strong> تقديم شهر واحد في العلاوة</p>
                            <p><strong>شكر وزاري:</strong> تقديم شهر واحد في العلاوة</p>
                            <p><strong>شكر رئاسي:</strong> تقديم ستة أشهر في العلاوة</p>
                        </div>
                        <div class="thanks-item">
                            <h4>ملاحظات:</h4>
                            <p>• يتم حساب تأثير التشكرات تلقائياً على تواريخ العلاوات والترفيعات</p>
                            <p>• التشكرات تؤثر على الترفيع بغض النظر عن تاريخها</p>
                            <p>• التشكرات تؤثر على العلاوة إذا كان تاريخها بعد آخر استحقاق</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>

    <script src="script.js"></script>
    <script src="simple-messages.js"></script>
    <script src="thanks-calculator.js"></script>
    <script src="add-thanks.js"></script>
</body>
</html>
