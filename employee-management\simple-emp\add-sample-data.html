<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة بيانات تجريبية - برنامج إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .sample-data-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .sample-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .btn-sample {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 1rem auto;
            width: fit-content;
        }

        .btn-sample:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            display: none;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .data-preview {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-chart-line logo-icon"></i>
                <span>برنامج إدارة العلاوات والترفيعات</span>
            </div>
            <div class="nav-links">
                <a href="index.html"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="employee-form.html"><i class="fas fa-user-plus"></i> إضافة موظف</a>
                <a href="add-thanks.html"><i class="fas fa-award"></i> إضافة شكر</a>
                <a href="thanks.html"><i class="fas fa-list"></i> عرض التشكرات</a>
            </div>
        </nav>
    </header>

    <main>
        <div class="sample-data-container">
            <div class="page-header">
                <h1><i class="fas fa-database"></i> إضافة بيانات تجريبية</h1>
                <p>إضافة موظفين وتشكرات تجريبية لتجربة النظام</p>
            </div>

            <div class="sample-card">
                <h3><i class="fas fa-info-circle"></i> معلومات</h3>
                <p>هذه الأداة ستقوم بإضافة بيانات تجريبية للنظام تشمل:</p>
                <ul>
                    <li>✅ <strong>5 موظفين</strong> بتخصصات مختلفة</li>
                    <li>✅ <strong>10 كتب شكر</strong> بأنواع مختلفة</li>
                    <li>✅ <strong>تواريخ واقعية</strong> للعلاوات والترفيعات</li>
                    <li>✅ <strong>بيانات متكاملة</strong> لتجربة جميع الميزات</li>
                </ul>

                <div class="status-message" id="statusMessage"></div>
                
                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <button class="btn-sample" id="addSampleDataBtn">
                    <i class="fas fa-plus"></i>
                    إضافة البيانات التجريبية
                </button>

                <div class="data-preview" id="dataPreview" style="display: none;">
                    <h4>البيانات المضافة:</h4>
                    <div id="previewContent"></div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-chart-line"></i> برنامج إدارة العلاوات والترفيعات
            </div>
            <p>جميع الحقوق محفوظة &copy; 2025 - تم إنشاء النظام بواسطة الفني حسن علي موسى</p>
        </div>
    </footer>

    <script>
        document.getElementById('addSampleDataBtn').addEventListener('click', function() {
            addSampleData();
        });

        function addSampleData() {
            const btn = document.getElementById('addSampleDataBtn');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const statusMessage = document.getElementById('statusMessage');
            const dataPreview = document.getElementById('dataPreview');
            const previewContent = document.getElementById('previewContent');

            // إخفاء الرسائل السابقة
            statusMessage.style.display = 'none';
            dataPreview.style.display = 'none';

            // إظهار شريط التقدم
            progressBar.style.display = 'block';
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';

            // محاكاة التقدم
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(interval);
                    
                    // إضافة البيانات الفعلية
                    try {
                        const result = createSampleData();
                        
                        // إظهار رسالة النجاح
                        statusMessage.className = 'status-message status-success';
                        statusMessage.innerHTML = `
                            <i class="fas fa-check-circle"></i>
                            تم إضافة البيانات التجريبية بنجاح!<br>
                            <small>تم إضافة ${result.employees} موظف و ${result.thanks} كتاب شكر</small>
                        `;
                        statusMessage.style.display = 'block';

                        // إظهار معاينة البيانات
                        previewContent.innerHTML = result.preview;
                        dataPreview.style.display = 'block';

                        // إعادة تعيين الزر
                        btn.innerHTML = '<i class="fas fa-check"></i> تم إضافة البيانات';
                        btn.style.background = '#10b981';

                        // إخفاء شريط التقدم
                        setTimeout(() => {
                            progressBar.style.display = 'none';
                        }, 1000);

                    } catch (error) {
                        // إظهار رسالة الخطأ
                        statusMessage.className = 'status-message status-error';
                        statusMessage.innerHTML = `
                            <i class="fas fa-exclamation-circle"></i>
                            حدث خطأ أثناء إضافة البيانات: ${error.message}
                        `;
                        statusMessage.style.display = 'block';

                        // إعادة تعيين الزر
                        btn.innerHTML = '<i class="fas fa-plus"></i> إضافة البيانات التجريبية';
                        btn.disabled = false;
                        progressBar.style.display = 'none';
                    }
                }
            }, 200);
        }

        function createSampleData() {
            // إنشاء موظفين تجريبيين
            const sampleEmployees = [
                {
                    id: 1,
                    name: "د. أحمد محمد علي",
                    employeeNumber: "2020001",
                    currentJobTitle: "أستاذ مساعد",
                    newJobTitle: "أستاذ مشارك",
                    workLocation: "كلية الهندسة",
                    appointmentDate: "2020-01-15",
                    currentSalary: 850000,
                    currentDueDate: "2024-01-15",
                    nextAllowanceDate: "2025-01-15",
                    nextPromotionDate: "2027-01-15"
                },
                {
                    id: 2,
                    name: "د. فاطمة حسن محمود",
                    employeeNumber: "2019002",
                    currentJobTitle: "مدرس",
                    newJobTitle: "أستاذ مساعد",
                    workLocation: "كلية الطب",
                    appointmentDate: "2019-09-01",
                    currentSalary: 750000,
                    currentDueDate: "2023-09-01",
                    nextAllowanceDate: "2024-09-01",
                    nextPromotionDate: "2026-09-01"
                },
                {
                    id: 3,
                    name: "م. علي حسين كريم",
                    employeeNumber: "2021003",
                    currentJobTitle: "مهندس أول",
                    newJobTitle: "مهندس خبير",
                    workLocation: "الشؤون الهندسية",
                    appointmentDate: "2021-03-10",
                    currentSalary: 650000,
                    currentDueDate: "2024-03-10",
                    nextAllowanceDate: "2025-03-10",
                    nextPromotionDate: "2028-03-10"
                },
                {
                    id: 4,
                    name: "أ. زينب عبد الله صالح",
                    employeeNumber: "2018004",
                    currentJobTitle: "موظف إداري أول",
                    newJobTitle: "موظف إداري خبير",
                    workLocation: "الشؤون الإدارية",
                    appointmentDate: "2018-06-20",
                    currentSalary: 550000,
                    currentDueDate: "2023-06-20",
                    nextAllowanceDate: "2024-06-20",
                    nextPromotionDate: "2025-06-20"
                },
                {
                    id: 5,
                    name: "د. محمد عبد الرحمن طه",
                    employeeNumber: "2017005",
                    currentJobTitle: "أستاذ مشارك",
                    newJobTitle: "أستاذ",
                    workLocation: "كلية العلوم",
                    appointmentDate: "2017-10-01",
                    currentSalary: 950000,
                    currentDueDate: "2023-10-01",
                    nextAllowanceDate: "2024-10-01",
                    nextPromotionDate: "2025-10-01"
                }
            ];

            // حفظ الموظفين
            localStorage.setItem('employees', JSON.stringify(sampleEmployees));

            // إنشاء تشكرات تجريبية
            const sampleThanks = [
                {
                    id: 1,
                    employeeId: 1,
                    type: "appreciation",
                    date: "2024-03-15",
                    number: "ج/123/2024",
                    issuer: "رئاسة الجامعة",
                    reason: "التميز في البحث العلمي",
                    createdAt: "2024-03-15T10:00:00.000Z"
                },
                {
                    id: 2,
                    employeeId: 2,
                    type: "ministerial",
                    date: "2024-02-20",
                    number: "و/456/2024",
                    issuer: "وزارة التعليم العالي والبحث العلمي",
                    reason: "الإنجاز المتميز في التدريس",
                    createdAt: "2024-02-20T10:00:00.000Z"
                },
                {
                    id: 3,
                    employeeId: 3,
                    type: "appreciation",
                    date: "2024-01-10",
                    number: "ج/789/2024",
                    issuer: "رئاسة الجامعة",
                    reason: "تطوير الأنظمة الهندسية",
                    createdAt: "2024-01-10T10:00:00.000Z"
                },
                {
                    id: 4,
                    employeeId: 4,
                    type: "appreciation",
                    date: "2023-12-05",
                    number: "ج/321/2023",
                    issuer: "رئاسة الجامعة",
                    reason: "التميز في الأداء الإداري",
                    createdAt: "2023-12-05T10:00:00.000Z"
                },
                {
                    id: 5,
                    employeeId: 5,
                    type: "presidential",
                    date: "2023-11-15",
                    number: "ر/654/2023",
                    issuer: "رئاسة مجلس الوزراء",
                    reason: "الإنجاز العلمي المتميز",
                    createdAt: "2023-11-15T10:00:00.000Z"
                }
            ];

            // حفظ التشكرات
            localStorage.setItem('thanks', JSON.stringify(sampleThanks));

            // إنشاء معاينة البيانات
            const preview = `
الموظفين المضافين:
${sampleEmployees.map(emp => `- ${emp.name} (${emp.employeeNumber})`).join('\n')}

التشكرات المضافة:
${sampleThanks.map(thank => `- ${sampleEmployees.find(emp => emp.id === thank.employeeId)?.name}: ${thank.type} (${thank.date})`).join('\n')}
            `;

            return {
                employees: sampleEmployees.length,
                thanks: sampleThanks.length,
                preview: preview
            };
        }
    </script>
</body>
</html>
