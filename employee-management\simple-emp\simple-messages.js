/**
 * ملف لإدارة الرسائل البسيطة
 */

// إنشاء عنصر الرسالة
let messageElement = null;

/**
 * عرض رسالة بسيطة
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع الرسالة (success, error)
 */
function showSimpleMessage(message, type = 'success') {
    // إزالة الرسالة السابقة إذا كانت موجودة
    if (messageElement) {
        document.body.removeChild(messageElement);
    }

    // إنشاء عنصر الرسالة
    messageElement = document.createElement('div');
    messageElement.className = `simple-message ${type}`;
    messageElement.textContent = message;
    
    // إضافة الرسالة إلى الصفحة
    document.body.appendChild(messageElement);
    
    // إخفاء الرسالة بعد 3 ثوانٍ
    setTimeout(function() {
        if (messageElement && messageElement.parentNode) {
            messageElement.classList.add('fade-out');
            setTimeout(function() {
                if (messageElement && messageElement.parentNode) {
                    document.body.removeChild(messageElement);
                    messageElement = null;
                }
            }, 500);
        }
    }, 3000);
}

// إضافة أنماط CSS للرسائل
(function addMessageStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .simple-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            animation: fadeIn 0.5s ease-out;
        }
        
        .simple-message.success {
            background-color: #4CAF50;
        }
        
        .simple-message.error {
            background-color: #F44336;
        }
        
        .simple-message.fade-out {
            opacity: 0;
            transition: opacity 0.5s;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translate(-50%, -20px);
            }
            to {
                opacity: 1;
                transform: translate(-50%, 0);
            }
        }
        
        /* تأثيرات الوضع المظلم */
        .dark-mode .simple-message.success {
            background-color: #2e7d32;
        }
        
        .dark-mode .simple-message.error {
            background-color: #c62828;
        }
    `;
    document.head.appendChild(style);
})();

// تصدير الوظائف
window.showSimpleMessage = showSimpleMessage;

// تعريف دالة showNotification لتستخدم showSimpleMessage
window.showNotification = function(message, type) {
    // تحويل نوع الإشعار إلى نوع الرسالة البسيطة
    let messageType = 'success';
    if (type === 'error' || type === 'warning') {
        messageType = 'error';
    }
    
    // عرض الرسالة البسيطة
    showSimpleMessage(message, messageType);
};
