// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة العناوين الوظيفية
    initJobTitlesPage();

    // إضافة مستمع للرسائل من النوافذ الأخرى
    window.addEventListener('message', function(event) {
        console.log('تم استلام رسالة في نافذة العناوين الوظيفية:', event.data);

        // التحقق من نوع الرسالة
        if (event.data && event.data.type === 'currentJobDescription') {
            console.log('تم استلام الوصف الوظيفي الحالي من النافذة الأم:', event.data.value);

            // تحديد الوصف الوظيفي في قائمة التصفية
            const filterSelect = document.getElementById('filterCategory');
            if (filterSelect && event.data.value) {
                filterSelect.value = event.data.value;

                // تطبيق التصفية
                applySearchAndFilter();

                // إضافة زر العودة إلى نموذج الموظف في أعلى الصفحة إذا لم يكن موجوداً
                if (!document.getElementById('returnToEmployeeForm')) {
                    const profileContainer = document.querySelector('.profile-container');
                    if (profileContainer) {
                        const returnButton = document.createElement('button');
                        returnButton.id = 'returnToEmployeeForm';
                        returnButton.className = 'btn btn-primary return-btn';
                        returnButton.innerHTML = '<i class="fas fa-arrow-left"></i> العودة إلى نموذج الموظف';
                        returnButton.style.marginBottom = '15px';

                        // إضافة مستمع حدث لزر العودة
                        returnButton.addEventListener('click', function() {
                            // التحقق من وجود صفحة سابقة في التخزين المحلي
                            const previousPage = localStorage.getItem('previousPage');
                            if (previousPage) {
                                window.location.href = previousPage;
                            } else {
                                window.location.href = 'employee-form.html';
                            }
                        });

                        // إضافة الزر قبل عنوان الصفحة
                        const pageTitle = profileContainer.querySelector('.profile-title');
                        if (pageTitle) {
                            profileContainer.insertBefore(returnButton, pageTitle);
                        } else {
                            profileContainer.prepend(returnButton);
                        }

                        // إضافة أنماط CSS للزر إذا لم تكن موجودة
                        if (!document.querySelector('style[data-id="return-btn-style"]')) {
                            const style = document.createElement('style');
                            style.setAttribute('data-id', 'return-btn-style');
                            style.textContent = `
                                .return-btn {
                                    display: block;
                                    margin-right: auto;
                                    margin-left: 0;
                                    padding: 0.5rem 1rem;
                                    font-size: 0.9rem;
                                }
                            `;
                            document.head.appendChild(style);
                        }
                    }
                }
            }
        }
    });
});

// تهيئة صفحة العناوين الوظيفية
function initJobTitlesPage() {
    console.log('بدء تهيئة صفحة العناوين الوظيفية');

    // التحقق مما إذا كانت هذه النافذة مفتوحة من نموذج الموظف
    const currentJobDescription = localStorage.getItem('currentJobDescription');
    if (currentJobDescription) {
        console.log('تم فتح الصفحة من نموذج الموظف مع الوصف الوظيفي:', currentJobDescription);

        // إضافة زر العودة إلى نموذج الموظف في أعلى الصفحة
        const profileContainer = document.querySelector('.profile-container');
        if (profileContainer && !document.getElementById('returnToEmployeeForm')) {
            const returnButton = document.createElement('button');
            returnButton.id = 'returnToEmployeeForm';
            returnButton.className = 'btn btn-primary return-btn';
            returnButton.innerHTML = '<i class="fas fa-arrow-left"></i> العودة إلى نموذج الموظف';
            returnButton.style.marginBottom = '15px';

            // إضافة مستمع حدث لزر العودة
            returnButton.addEventListener('click', function() {
                // التحقق من وجود صفحة سابقة في التخزين المحلي
                const previousPage = localStorage.getItem('previousPage');
                if (previousPage) {
                    window.location.href = previousPage;
                } else {
                    window.location.href = 'employee-form.html';
                }
            });

            // إضافة الزر قبل عنوان الصفحة
            const pageTitle = profileContainer.querySelector('.profile-title');
            if (pageTitle) {
                profileContainer.insertBefore(returnButton, pageTitle);
            } else {
                profileContainer.prepend(returnButton);
            }

            // إضافة أنماط CSS للزر
            const style = document.createElement('style');
            style.textContent = `
                .return-btn {
                    display: block;
                    margin-right: auto;
                    margin-left: 0;
                    padding: 0.5rem 1rem;
                    font-size: 0.9rem;
                }
            `;
            document.head.appendChild(style);
        }

        // تصفية العناوين الوظيفية حسب الوصف الوظيفي المحدد
        const filterSelect = document.getElementById('filterCategory');
        if (filterSelect) {
            filterSelect.value = currentJobDescription;
            // تطبيق التصفية
            setTimeout(() => {
                applySearchAndFilter();
            }, 100);
        }
    }

    // تحميل العناوين الوظيفية
    loadJobTitles();

    // تهيئة نموذج إضافة عنوان وظيفي
    initAddJobTitleForm();

    // تهيئة البحث والتصفية
    initSearchAndFilter();

    // تهيئة النوافذ المنبثقة
    initModals();
}

// تحميل العناوين الوظيفية
function loadJobTitles() {
    // في الإصدار النهائي، سيتم استبدال هذا بطلب API لجلب البيانات من الخادم
    // هنا نستخدم بيانات تجريبية للعرض

    // التحقق من وجود بيانات في التخزين المحلي
    let jobTitles = localStorage.getItem('jobTitles');

    if (!jobTitles) {
        // استخدام قائمة فارغة بدلاً من البيانات الافتراضية
        jobTitles = [];

        // حفظ البيانات في التخزين المحلي
        localStorage.setItem('jobTitles', JSON.stringify(jobTitles));
    } else {
        // تحويل البيانات من JSON إلى كائن JavaScript
        jobTitles = JSON.parse(jobTitles);
    }

    // عرض البيانات في الجدول
    displayJobTitles(jobTitles);
}

// عرض العناوين الوظيفية في الجدول
function displayJobTitles(jobTitles) {
    const tableBody = document.querySelector('#jobTitlesTable tbody');
    if (!tableBody) return;

    // مسح محتوى الجدول
    tableBody.innerHTML = '';

    // إذا لم تكن هناك عناوين وظيفية، عرض رسالة
    if (jobTitles.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="text-center">لا توجد عناوين وظيفية. أضف عنوان وظيفي جديد.</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // إضافة الصفوف إلى الجدول
    jobTitles.forEach((title, index) => {
        const row = document.createElement('tr');

        // تحويل وصف العنوان الوظيفي إلى نص عربي
        let categoryText = '';
        switch (title.category) {
            case 'teaching':
                categoryText = 'تدريسي';
                break;
            case 'technical':
                categoryText = 'فني';
                break;
            case 'administrative':
                categoryText = 'اداري';
                break;
            default:
                categoryText = title.category;
        }

        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${title.name}</td>
            <td>${categoryText}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${title.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${title.id}" data-name="${title.name}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // إضافة مستمعي الأحداث لأزرار التعديل والحذف
    addActionButtonsEventListeners();
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addActionButtonsEventListeners() {
    // أزرار التعديل
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const titleId = this.getAttribute('data-id');
            openEditModal(titleId);
        });
    });

    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const titleId = this.getAttribute('data-id');
            const titleName = this.getAttribute('data-name');
            openDeleteModal(titleId, titleName);
        });
    });
}

// تهيئة نموذج إضافة عنوان وظيفي
function initAddJobTitleForm() {
    const addForm = document.getElementById('addJobTitleForm');
    if (!addForm) return;

    addForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // الحصول على قيم الحقول
        const name = document.getElementById('jobTitleName').value.trim();
        const category = document.getElementById('jobCategory').value;
        const newJobTitle = document.getElementById('newJobTitle').value.trim();

        // التحقق من صحة البيانات
        if (!name) {
            alert('يرجى إدخال اسم العنوان الوظيفي');
            return;
        }

        if (!category) {
            alert('يرجى اختيار الفئة الوظيفية');
            return;
        }

        // إضافة العنوان الوظيفي الجديد
        addJobTitle(name, category, newJobTitle);

        // إعادة تعيين النموذج
        this.reset();
    });
}

// تحديث خيارات العنوان الوظيفي التالي
function updateNextJobTitleOptions(categorySelectId, nextJobTitleSelectId) {
    const categorySelect = document.getElementById(categorySelectId);
    const nextJobTitleSelect = document.getElementById(nextJobTitleSelectId);

    if (!categorySelect || !nextJobTitleSelect) return;

    const selectedCategory = categorySelect.value;

    // حفظ القيمة المحددة حالياً
    const currentValue = nextJobTitleSelect.value;

    // مسح الخيارات الحالية
    nextJobTitleSelect.innerHTML = '<option value="">لا يوجد (آخر عنوان في المسار)</option>';

    if (!selectedCategory) return;

    // الحصول على العناوين الوظيفية
    const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');

    // تصفية العناوين الوظيفية حسب الفئة المحددة
    const filteredTitles = jobTitles.filter(title => title.category === selectedCategory);

    // إضافة الخيارات
    filteredTitles.forEach(title => {
        const option = document.createElement('option');
        option.value = title.id;
        option.textContent = title.name;
        nextJobTitleSelect.appendChild(option);
    });

    // محاولة استعادة القيمة المحددة سابقاً
    if (currentValue) {
        // التحقق من وجود الخيار في القائمة الجديدة
        for (let i = 0; i < nextJobTitleSelect.options.length; i++) {
            if (nextJobTitleSelect.options[i].value === currentValue) {
                nextJobTitleSelect.value = currentValue;
                break;
            }
        }
    }
}

// إضافة عنوان وظيفي جديد
function addJobTitle(name, category, newJobTitle) {
    console.log('بدء إضافة عنوان وظيفي جديد...');
    console.log('البيانات المدخلة:', { name, category, newJobTitle });

    // الحصول على العناوين الوظيفية الحالية
    let jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    console.log('عدد العناوين الوظيفية الحالية:', jobTitles.length);

    // إنشاء معرف فريد جديد
    const newId = jobTitles.length > 0 ? Math.max(...jobTitles.map(title => title.id)) + 1 : 1;
    console.log('المعرف الجديد:', newId);

    // إنشاء العنوان الوظيفي الجديد
    const newTitle = {
        id: newId,
        name: name,
        category: category,
        newJobTitle: newJobTitle || ''
    };
    console.log('العنوان الوظيفي الجديد:', newTitle);

    // إضافة العنوان الوظيفي الجديد إلى المصفوفة
    jobTitles.push(newTitle);
    console.log('تمت إضافة العنوان الوظيفي الجديد إلى المصفوفة');

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('jobTitles', JSON.stringify(jobTitles));
    console.log('تم حفظ البيانات المحدثة في التخزين المحلي');

    // تحديث عرض الجدول
    displayJobTitles(jobTitles);
    console.log('تم تحديث عرض الجدول');

    // عرض رسالة نجاح
    showNotification('تمت الإضافة', 'success');
    console.log('تم إضافة العنوان الوظيفي بنجاح');

    // إعلام الصفحات الأخرى بالتغيير
    const timestamp = new Date().toISOString();
    localStorage.setItem('jobTitlesUpdated', timestamp);
    localStorage.setItem('needsUpdate', 'true');
    console.log('تم تحديث مؤشر التحديث في التخزين المحلي:', timestamp);

    // إرسال رسالة إلى جميع النوافذ المفتوحة
    try {
        console.log('محاولة إرسال رسائل إلى النوافذ الأخرى...');

        // إرسال رسالة إلى النافذة الأم
        if (window.opener) {
            console.log('النافذة الأم موجودة، إرسال رسالة...');

            try {
                // إرسال رسالة بالتغيير
                window.opener.postMessage({
                    type: 'jobTitlesChanged',
                    data: newTitle,
                    timestamp: timestamp
                }, '*');
                console.log('تم إرسال رسالة إلى النافذة الأم');

                // محاولة الوصول إلى عناصر النافذة الأم
                if (window.opener.document) {
                    console.log('يمكن الوصول إلى وثيقة النافذة الأم');

                    const jobDescriptionElement = window.opener.document.getElementById('jobDescription');
                    if (jobDescriptionElement) {
                        console.log('تم العثور على عنصر الوصف الوظيفي في النافذة الأم');

                        const jobDescriptionValue = jobDescriptionElement.value;
                        console.log('قيمة الوصف الوظيفي في النافذة الأم:', jobDescriptionValue);

                        // التحقق مما إذا كان العنوان الوظيفي الجديد ينتمي إلى الفئة المحددة
                        if (jobDescriptionValue === category) {
                            console.log('العنوان الوظيفي الجديد ينتمي إلى الفئة المحددة حالياً في النافذة الأم');

                            // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
                            localStorage.removeItem('jobTitlesCache');

                            // استدعاء وظيفة التحديث في النافذة الأم
                            if (typeof window.opener.updateJobTitlesList === 'function') {
                                console.log('استدعاء وظيفة updateJobTitlesList في النافذة الأم');
                                window.opener.updateJobTitlesList(jobDescriptionValue);

                                // تم إزالة الإشعار هنا
                            } else {
                                console.error('وظيفة updateJobTitlesList غير متوفرة في النافذة الأم');
                            }
                        } else {
                            console.log('العنوان الوظيفي الجديد لا ينتمي إلى الفئة المحددة حالياً في النافذة الأم');
                        }
                    } else {
                        console.log('لم يتم العثور على عنصر الوصف الوظيفي في النافذة الأم');
                    }
                } else {
                    console.log('لا يمكن الوصول إلى وثيقة النافذة الأم');
                }
            } catch (innerError) {
                console.error('خطأ أثناء محاولة الوصول إلى النافذة الأم:', innerError);
            }
        } else {
            console.log('النافذة الأم غير موجودة');
        }

        // إرسال رسالة إلى النافذة الحالية
        console.log('إرسال رسالة إلى النافذة الحالية');
        window.postMessage({
            type: 'jobTitlesChanged',
            data: newTitle,
            timestamp: timestamp
        }, '*');

        // تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage
        console.log('تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage');
        localStorage.setItem('jobTitlesUpdated', new Date().toISOString());

        // محاولة إنشاء حدث storage يدوياً
        try {
            console.log('محاولة إنشاء حدث storage يدوياً');
            const storageEvent = new StorageEvent('storage', {
                key: 'jobTitlesUpdated',
                newValue: timestamp,
                oldValue: null,
                storageArea: localStorage,
                url: window.location.href
            });
            window.dispatchEvent(storageEvent);
            console.log('تم إطلاق حدث storage يدوياً');
        } catch (storageError) {
            console.error('خطأ في إنشاء حدث storage:', storageError);
        }
    } catch (e) {
        console.error('خطأ في إرسال رسالة:', e);
    }

    console.log('اكتملت عملية إضافة العنوان الوظيفي الجديد بنجاح');
}

// تهيئة البحث والتصفية
function initSearchAndFilter() {
    const searchInput = document.getElementById('searchJobTitle');
    const filterSelect = document.getElementById('filterCategory');

    if (searchInput) {
        searchInput.addEventListener('input', applySearchAndFilter);
    }

    if (filterSelect) {
        filterSelect.addEventListener('change', applySearchAndFilter);
    }
}

// تطبيق البحث والتصفية
function applySearchAndFilter() {
    const searchInput = document.getElementById('searchJobTitle');
    const filterSelect = document.getElementById('filterCategory');

    if (!searchInput || !filterSelect) return;

    const searchTerm = searchInput.value.trim().toLowerCase();
    const filterCategory = filterSelect.value;

    // الحصول على العناوين الوظيفية
    const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');

    // تصفية العناوين الوظيفية بناءً على مصطلح البحث والفئة
    const filteredTitles = jobTitles.filter(title => {
        const matchesSearch = title.name.toLowerCase().includes(searchTerm);
        const matchesCategory = !filterCategory || title.category === filterCategory;
        return matchesSearch && matchesCategory;
    });

    // عرض النتائج المصفاة
    displayJobTitles(filteredTitles);
}

// تهيئة النوافذ المنبثقة
function initModals() {
    // الحصول على عناصر النوافذ المنبثقة
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const closeButtons = document.querySelectorAll('.close-modal');

    // أزرار الإغلاق
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal);
        });
    });

    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // زر إلغاء التعديل
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', function() {
            closeModal(editModal);
        });
    }

    // زر حفظ التعديل
    const saveEditBtn = document.getElementById('saveEditBtn');
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            saveEditedJobTitle();
        });
    }

    // زر إلغاء الحذف
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', function() {
            closeModal(deleteModal);
        });
    }

    // زر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const titleId = this.getAttribute('data-id');
            deleteJobTitle(titleId);
            closeModal(deleteModal);
        });
    }
}

// فتح نافذة التعديل
function openEditModal(titleId) {
    // الحصول على العنوان الوظيفي المراد تعديله
    const jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    const title = jobTitles.find(title => title.id == titleId);

    if (!title) return;

    // ملء حقول النموذج بالبيانات الحالية
    document.getElementById('editJobTitleId').value = title.id;
    document.getElementById('editJobTitleName').value = title.name;
    document.getElementById('editJobCategory').value = title.category;
    document.getElementById('editNewJobTitle').value = title.newJobTitle || '';

    // فتح النافذة
    const editModal = document.getElementById('editModal');
    openModal(editModal);
}

// فتح نافذة تأكيد الحذف
function openDeleteModal(titleId, titleName) {
    // عرض اسم العنوان الوظيفي في رسالة التأكيد
    document.getElementById('deleteJobTitleName').textContent = titleName;

    // تعيين معرف العنوان الوظيفي لزر التأكيد
    document.getElementById('confirmDeleteBtn').setAttribute('data-id', titleId);

    // فتح النافذة
    const deleteModal = document.getElementById('deleteModal');
    openModal(deleteModal);
}

// حفظ العنوان الوظيفي المعدل
function saveEditedJobTitle() {
    console.log('بدء حفظ العنوان الوظيفي المعدل...');

    // الحصول على قيم الحقول
    const id = document.getElementById('editJobTitleId').value;
    const name = document.getElementById('editJobTitleName').value.trim();
    const category = document.getElementById('editJobCategory').value;
    const newJobTitle = document.getElementById('editNewJobTitle').value.trim();

    console.log('بيانات العنوان الوظيفي المعدل:', { id, name, category, newJobTitle });

    // التحقق من صحة البيانات
    if (!name) {
        alert('يرجى إدخال اسم العنوان الوظيفي');
        return;
    }

    if (!category) {
        alert('يرجى اختيار الفئة الوظيفية');
        return;
    }

    // الحصول على العناوين الوظيفية الحالية
    let jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    console.log('عدد العناوين الوظيفية الحالية:', jobTitles.length);

    // البحث عن العنوان الوظيفي وتحديثه
    const index = jobTitles.findIndex(title => title.id == id);
    console.log('مؤشر العنوان الوظيفي في المصفوفة:', index);

    if (index !== -1) {
        // الحصول على العنوان الوظيفي القديم قبل التحديث
        const oldTitle = { ...jobTitles[index] };
        console.log('العنوان الوظيفي القديم:', oldTitle);

        // تحديث العنوان الوظيفي
        jobTitles[index] = {
            id: parseInt(id),
            name: name,
            category: category,
            newJobTitle: newJobTitle || ''
        };
        console.log('العنوان الوظيفي الجديد:', jobTitles[index]);

        // حفظ البيانات المحدثة في التخزين المحلي
        localStorage.setItem('jobTitles', JSON.stringify(jobTitles));
        console.log('تم حفظ البيانات المحدثة في التخزين المحلي');

        // تحديث عرض الجدول
        displayJobTitles(jobTitles);
        console.log('تم تحديث عرض الجدول');

        // إغلاق النافذة
        closeModal(document.getElementById('editModal'));
        console.log('تم إغلاق نافذة التعديل');

        // عرض رسالة نجاح
        showNotification('تم التعديل', 'success');
        console.log('تم تحديث العنوان الوظيفي بنجاح');

        // إعلام الصفحات الأخرى بالتغيير
        const timestamp = new Date().toISOString();
        localStorage.setItem('jobTitlesUpdated', timestamp);
        localStorage.setItem('needsUpdate', 'true');
        console.log('تم تحديث مؤشر التحديث في التخزين المحلي:', timestamp);

        // إرسال رسالة إلى جميع النوافذ المفتوحة
        try {
            console.log('محاولة إرسال رسائل إلى النوافذ الأخرى...');

            // إرسال رسالة إلى النافذة الأم
            if (window.opener) {
                console.log('النافذة الأم موجودة، إرسال رسالة...');

                try {
                    // إرسال رسالة بالتغيير
                    window.opener.postMessage({
                        type: 'jobTitlesChanged',
                        action: 'edit',
                        oldData: oldTitle,
                        newData: jobTitles[index],
                        timestamp: timestamp
                    }, '*');
                    console.log('تم إرسال رسالة إلى النافذة الأم');

                    // محاولة الوصول إلى عناصر النافذة الأم
                    if (window.opener.document) {
                        console.log('يمكن الوصول إلى وثيقة النافذة الأم');

                        const jobDescriptionElement = window.opener.document.getElementById('jobDescription');
                        if (jobDescriptionElement) {
                            console.log('تم العثور على عنصر الوصف الوظيفي في النافذة الأم');

                            const jobDescriptionValue = jobDescriptionElement.value;
                            console.log('قيمة الوصف الوظيفي في النافذة الأم:', jobDescriptionValue);

                            // التحقق مما إذا كان العنوان الوظيفي المعدل ينتمي إلى الفئة المحددة
                            const needsUpdate = jobDescriptionValue === category || jobDescriptionValue === oldTitle.category;

                            if (needsUpdate) {
                                console.log('العنوان الوظيفي المعدل ينتمي إلى الفئة المحددة حالياً في النافذة الأم أو كان ينتمي إليها قبل التعديل');

                                // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
                                localStorage.removeItem('jobTitlesCache');

                                // استدعاء وظيفة التحديث في النافذة الأم
                                if (typeof window.opener.updateJobTitlesList === 'function') {
                                    console.log('استدعاء وظيفة updateJobTitlesList في النافذة الأم');
                                    window.opener.updateJobTitlesList(jobDescriptionValue);

                                    // عرض إشعار في النافذة الأم
                                    if (typeof window.opener.showNotification === 'function') {
                                        console.log('عرض إشعار في النافذة الأم');
                                        window.opener.showNotification('تم تحديث العناوين الوظيفية', 'success');
                                    }
                                } else {
                                    console.error('وظيفة updateJobTitlesList غير متوفرة في النافذة الأم');
                                }
                            } else {
                                console.log('العنوان الوظيفي المعدل لا ينتمي إلى الفئة المحددة حالياً في النافذة الأم');
                            }
                        } else {
                            console.log('لم يتم العثور على عنصر الوصف الوظيفي في النافذة الأم');
                        }
                    } else {
                        console.log('لا يمكن الوصول إلى وثيقة النافذة الأم');
                    }
                } catch (innerError) {
                    console.error('خطأ أثناء محاولة الوصول إلى النافذة الأم:', innerError);
                }
            } else {
                console.log('النافذة الأم غير موجودة');
            }

            // إرسال رسالة إلى النافذة الحالية
            console.log('إرسال رسالة إلى النافذة الحالية');
            window.postMessage({
                type: 'jobTitlesChanged',
                action: 'edit',
                oldData: oldTitle,
                newData: jobTitles[index],
                timestamp: timestamp
            }, '*');

            // تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage
            console.log('تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage');
            localStorage.setItem('jobTitlesUpdated', new Date().toISOString());

            // محاولة إنشاء حدث storage يدوياً
            try {
                console.log('محاولة إنشاء حدث storage يدوياً');
                const storageEvent = new StorageEvent('storage', {
                    key: 'jobTitlesUpdated',
                    newValue: timestamp,
                    oldValue: null,
                    storageArea: localStorage,
                    url: window.location.href
                });
                window.dispatchEvent(storageEvent);
                console.log('تم إطلاق حدث storage يدوياً');
            } catch (storageError) {
                console.error('خطأ في إنشاء حدث storage:', storageError);
            }
        } catch (e) {
            console.error('خطأ في إرسال رسالة:', e);
        }

        console.log('اكتملت عملية تحديث العنوان الوظيفي بنجاح');
    } else {
        console.error('لم يتم العثور على العنوان الوظيفي المطلوب تعديله');
        alert('لم يتم العثور على العنوان الوظيفي المطلوب تعديله');
    }
}

// حذف العنوان الوظيفي
function deleteJobTitle(titleId) {
    console.log('بدء حذف العنوان الوظيفي...', titleId);

    // الحصول على العناوين الوظيفية الحالية
    let jobTitles = JSON.parse(localStorage.getItem('jobTitles') || '[]');
    console.log('عدد العناوين الوظيفية الحالية:', jobTitles.length);

    // الحصول على العنوان الوظيفي المراد حذفه
    const titleToDelete = jobTitles.find(title => title.id == titleId);
    if (!titleToDelete) {
        console.error('لم يتم العثور على العنوان الوظيفي المراد حذفه');
        return;
    }
    console.log('العنوان الوظيفي المراد حذفه:', titleToDelete);

    // حذف العنوان الوظيفي
    jobTitles = jobTitles.filter(title => title.id != titleId);
    console.log('عدد العناوين الوظيفية بعد الحذف:', jobTitles.length);

    // حفظ البيانات المحدثة في التخزين المحلي
    localStorage.setItem('jobTitles', JSON.stringify(jobTitles));
    console.log('تم حفظ البيانات المحدثة في التخزين المحلي');

    // تحديث عرض الجدول
    displayJobTitles(jobTitles);
    console.log('تم تحديث عرض الجدول');

    // عرض رسالة نجاح
    showNotification('تم الحذف', 'success');
    console.log('تم حذف العنوان الوظيفي بنجاح');

    // إعلام الصفحات الأخرى بالتغيير
    const timestamp = new Date().toISOString();
    localStorage.setItem('jobTitlesUpdated', timestamp);
    localStorage.setItem('needsUpdate', 'true');
    console.log('تم تحديث مؤشر التحديث في التخزين المحلي:', timestamp);

    // إرسال رسالة إلى جميع النوافذ المفتوحة
    try {
        console.log('محاولة إرسال رسائل إلى النوافذ الأخرى...');

        // إرسال رسالة إلى النافذة الأم
        if (window.opener) {
            console.log('النافذة الأم موجودة، إرسال رسالة...');

            try {
                // إرسال رسالة بالتغيير
                window.opener.postMessage({
                    type: 'jobTitlesChanged',
                    action: 'delete',
                    data: titleToDelete,
                    timestamp: timestamp
                }, '*');
                console.log('تم إرسال رسالة إلى النافذة الأم');

                // محاولة الوصول إلى عناصر النافذة الأم
                if (window.opener.document) {
                    console.log('يمكن الوصول إلى وثيقة النافذة الأم');

                    const jobDescriptionElement = window.opener.document.getElementById('jobDescription');
                    if (jobDescriptionElement) {
                        console.log('تم العثور على عنصر الوصف الوظيفي في النافذة الأم');

                        const jobDescriptionValue = jobDescriptionElement.value;
                        console.log('قيمة الوصف الوظيفي في النافذة الأم:', jobDescriptionValue);

                        // التحقق مما إذا كان العنوان الوظيفي المحذوف ينتمي إلى الفئة المحددة
                        if (jobDescriptionValue === titleToDelete.category) {
                            console.log('العنوان الوظيفي المحذوف ينتمي إلى الفئة المحددة حالياً في النافذة الأم');

                            // مسح ذاكرة التخزين المؤقت للعناوين الوظيفية
                            localStorage.removeItem('jobTitlesCache');

                            // استدعاء وظيفة التحديث في النافذة الأم
                            if (typeof window.opener.updateJobTitlesList === 'function') {
                                console.log('استدعاء وظيفة updateJobTitlesList في النافذة الأم');
                                window.opener.updateJobTitlesList(jobDescriptionValue);

                                // تم إزالة الإشعار هنا
                            } else {
                                console.error('وظيفة updateJobTitlesList غير متوفرة في النافذة الأم');
                            }
                        } else {
                            console.log('العنوان الوظيفي المحذوف لا ينتمي إلى الفئة المحددة حالياً في النافذة الأم');
                        }
                    } else {
                        console.log('لم يتم العثور على عنصر الوصف الوظيفي في النافذة الأم');
                    }
                } else {
                    console.log('لا يمكن الوصول إلى وثيقة النافذة الأم');
                }
            } catch (innerError) {
                console.error('خطأ أثناء محاولة الوصول إلى النافذة الأم:', innerError);
            }
        } else {
            console.log('النافذة الأم غير موجودة');
        }

        // إرسال رسالة إلى النافذة الحالية
        console.log('إرسال رسالة إلى النافذة الحالية');
        window.postMessage({
            type: 'jobTitlesChanged',
            action: 'delete',
            data: titleToDelete,
            timestamp: timestamp
        }, '*');

        // تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage
        console.log('تحديث التخزين المحلي مرة أخرى لضمان إطلاق حدث storage');
        localStorage.setItem('jobTitlesUpdated', new Date().toISOString());

        // محاولة إنشاء حدث storage يدوياً
        try {
            console.log('محاولة إنشاء حدث storage يدوياً');
            const storageEvent = new StorageEvent('storage', {
                key: 'jobTitlesUpdated',
                newValue: timestamp,
                oldValue: null,
                storageArea: localStorage,
                url: window.location.href
            });
            window.dispatchEvent(storageEvent);
            console.log('تم إطلاق حدث storage يدوياً');
        } catch (storageError) {
            console.error('خطأ في إنشاء حدث storage:', storageError);
        }
    } catch (e) {
        console.error('خطأ في إرسال رسالة:', e);
    }

    console.log('اكتملت عملية حذف العنوان الوظيفي بنجاح');
}

// فتح نافذة منبثقة
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
    }
}

// إغلاق نافذة منبثقة
function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إضافة مستمع حدث لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', function() {
        notification.remove();
    });

    // إزالة الإشعار تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
