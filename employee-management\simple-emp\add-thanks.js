// متغيرات عامة
let selectedEmployeeId = null;
let selectedThanksType = null;

// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة إضافة الشكر
    initAddThanksPage();
});

// تهيئة صفحة إضافة الشكر
function initAddThanksPage() {
    console.log('تهيئة صفحة إضافة الشكر...');

    // تهيئة البحث عن الموظفين
    initEmployeeSearch();

    // تهيئة اختيار نوع الشكر
    initThanksTypeSelection();

    // تهيئة النموذج
    initForm();

    // تحميل آخر التشكرات
    loadRecentThanks();

    // تعيين التاريخ الحالي
    document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];

    console.log('تم تهيئة صفحة إضافة الشكر بنجاح');
}

// تهيئة البحث عن الموظفين
function initEmployeeSearch() {
    const searchInput = document.getElementById('employeeSearch');
    const dropdown = document.getElementById('employeeDropdown');

    if (!searchInput || !dropdown) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim().toLowerCase();

        if (searchTerm.length < 2) {
            dropdown.style.display = 'none';
            return;
        }

        // البحث في الموظفين
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const filteredEmployees = employees.filter(emp =>
            emp.name.toLowerCase().includes(searchTerm) ||
            emp.id.toString().includes(searchTerm) ||
            (emp.employeeNumber && emp.employeeNumber.toString().includes(searchTerm))
        );

        // عرض النتائج
        displayEmployeeOptions(filteredEmployees);
    });

    // إخفاء القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.style.display = 'none';
        }
    });
}

// عرض خيارات الموظفين
function displayEmployeeOptions(employees) {
    const dropdown = document.getElementById('employeeDropdown');

    if (employees.length === 0) {
        dropdown.innerHTML = '<div class="employee-option">لا توجد نتائج</div>';
        dropdown.style.display = 'block';
        return;
    }

    dropdown.innerHTML = '';

    employees.forEach(employee => {
        const option = document.createElement('div');
        option.className = 'employee-option';
        option.innerHTML = `
            <strong>${employee.name}</strong><br>
            <small>الرقم الوظيفي: ${employee.employeeNumber || employee.id} | ${employee.currentJobTitle || 'غير محدد'}</small>
        `;

        option.addEventListener('click', function() {
            selectEmployee(employee);
        });

        dropdown.appendChild(option);
    });

    dropdown.style.display = 'block';
}

// اختيار موظف
function selectEmployee(employee) {
    selectedEmployeeId = employee.id;

    // تحديث حقل البحث
    document.getElementById('employeeSearch').value = employee.name;

    // إظهار الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.textContent = `${employee.name} - الرقم الوظيفي: ${employee.employeeNumber || employee.id}`;
    selectedDiv.style.display = 'flex';

    // إخفاء القائمة المنسدلة
    document.getElementById('employeeDropdown').style.display = 'none';

    console.log('تم اختيار الموظف:', employee.name);
}

// تهيئة اختيار نوع الشكر
function initThanksTypeSelection() {
    const typeCards = document.querySelectorAll('.thanks-type-card');

    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            typeCards.forEach(c => c.classList.remove('selected'));

            // تحديد البطاقة الحالية
            this.classList.add('selected');

            // حفظ نوع الشكر المختار
            selectedThanksType = this.dataset.type;

            // تحديث الجهة المانحة تلقائياً
            updateIssuerField(selectedThanksType);

            // عرض تأثير الشكر إذا كان هناك موظف مختار
            if (selectedEmployeeId) {
                showThanksImpact();
            }

            console.log('تم اختيار نوع الشكر:', selectedThanksType);
        });
    });
}

// تحديث حقل الجهة المانحة
function updateIssuerField(type) {
    const issuerField = document.getElementById('thanksIssuer');

    switch (type) {
        case 'appreciation':
            issuerField.value = 'رئاسة الجامعة';
            break;
        case 'ministerial':
            issuerField.value = 'وزارة التعليم العالي والبحث العلمي';
            break;
        case 'presidential':
            issuerField.value = 'رئاسة مجلس الوزراء';
            break;
        default:
            issuerField.value = '';
    }
}

// تهيئة النموذج
function initForm() {
    const form = document.getElementById('addThanksForm');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitThanks();
        });
    }
}

// إرسال الشكر
function submitThanks() {
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return;
    }

    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الحفظ...';
    submitBtn.disabled = true;

    // تأخير قصير لإظهار التأثير البصري
    setTimeout(() => {
        // جمع البيانات
        const thanksData = {
            employeeId: selectedEmployeeId,
            type: selectedThanksType,
            date: document.getElementById('thanksDate').value,
            number: document.getElementById('thanksNumber').value,
            issuer: document.getElementById('thanksIssuer').value,
            reason: document.getElementById('thanksReason').value,
            createdAt: new Date().toISOString()
        };

        // حفظ الشكر
        if (saveThanks(thanksData)) {
            // تأثير النجاح
            document.querySelector('.thanks-form-card').classList.add('success-animation');

            // عرض رسالة نجاح
            showMessage('تم إضافة الشكر بنجاح وتحديث تواريخ الموظف', 'success');

            // إعادة تعيين النموذج
            setTimeout(() => {
                resetForm();
                // تحديث قائمة آخر التشكرات
                loadRecentThanks();
            }, 600);

        } else {
            showMessage('حدث خطأ أثناء حفظ الشكر', 'error');
        }

        // إعادة تعيين الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // إزالة تأثير النجاح
        setTimeout(() => {
            document.querySelector('.thanks-form-card').classList.remove('success-animation');
        }, 600);

    }, 500);
}

// التحقق من صحة النموذج
function validateForm() {
    // إزالة تنسيق الأخطاء السابقة
    document.querySelectorAll('.form-validation').forEach(el => {
        el.classList.remove('form-validation');
    });

    let isValid = true;

    if (!selectedEmployeeId) {
        showMessage('يرجى اختيار موظف', 'error');
        document.getElementById('employeeSearch').classList.add('form-validation');
        isValid = false;
    }

    if (!selectedThanksType) {
        showMessage('يرجى اختيار نوع الشكر', 'error');
        document.querySelectorAll('.thanks-type-card').forEach(card => {
            card.style.borderColor = '#ef4444';
        });
        isValid = false;
    }

    const date = document.getElementById('thanksDate').value;
    if (!date) {
        showMessage('يرجى إدخال تاريخ الشكر', 'error');
        document.getElementById('thanksDate').classList.add('form-validation');
        isValid = false;
    } else {
        // التحقق من أن التاريخ ليس في المستقبل
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // نهاية اليوم

        if (selectedDate > today) {
            showMessage('لا يمكن أن يكون تاريخ الشكر في المستقبل', 'error');
            document.getElementById('thanksDate').classList.add('form-validation');
            isValid = false;
        }
    }

    return isValid;
}

// حفظ الشكر
function saveThanks(thanksData) {
    try {
        // الحصول على التشكرات الحالية
        let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

        // إنشاء معرف فريد
        const newId = thanks.length > 0 ? Math.max(...thanks.map(t => t.id || 0)) + 1 : 1;
        thanksData.id = newId;

        // إضافة الشكر الجديد
        thanks.push(thanksData);

        // حفظ في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // تحديث تواريخ الموظف باستخدام حاسبة التشكرات
        if (window.thanksCalculator) {
            window.thanksCalculator.updateNewDueDateBasedOnThanks(selectedEmployeeId);
            window.thanksCalculator.updateNextPromotionDateBasedOnThanks(selectedEmployeeId);
        }

        console.log('تم حفظ الشكر بنجاح:', thanksData);
        return true;

    } catch (error) {
        console.error('خطأ في حفظ الشكر:', error);
        return false;
    }
}

// إعادة تعيين النموذج
function resetForm() {
    // إعادة تعيين المتغيرات
    selectedEmployeeId = null;
    selectedThanksType = null;

    // إعادة تعيين حقول النموذج
    document.getElementById('employeeSearch').value = '';
    document.getElementById('selectedEmployee').style.display = 'none';
    document.getElementById('thanksNumber').value = '';
    document.getElementById('thanksIssuer').value = '';
    document.getElementById('thanksReason').value = '';

    // إزالة التحديد من بطاقات نوع الشكر
    document.querySelectorAll('.thanks-type-card').forEach(card => {
        card.classList.remove('selected');
        card.style.borderColor = ''; // إزالة لون الخطأ
    });

    // إخفاء معاينة التأثير
    hideThanksImpact();

    // تعيين التاريخ الحالي
    document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];

    console.log('تم إعادة تعيين النموذج');
}

// تحميل آخر التشكرات
function loadRecentThanks() {
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    // ترتيب التشكرات حسب تاريخ الإنشاء (الأحدث أولاً)
    thanks.sort((a, b) => new Date(b.createdAt || b.date) - new Date(a.createdAt || a.date));

    // أخذ آخر 5 تشكرات
    const recentThanks = thanks.slice(0, 5);

    const container = document.getElementById('recentThanksList');

    if (recentThanks.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280;">لا توجد تشكرات مضافة بعد</p>';
        return;
    }

    container.innerHTML = '';

    recentThanks.forEach(thank => {
        const employee = employees.find(emp => emp.id == thank.employeeId);
        const employeeName = employee ? employee.name : 'موظف غير موجود';

        let typeText = '';
        switch (thank.type) {
            case 'appreciation':
                typeText = 'شكر جامعي';
                break;
            case 'ministerial':
                typeText = 'شكر وزاري';
                break;
            case 'presidential':
                typeText = 'شكر رئاسي';
                break;
            default:
                typeText = thank.type || 'غير محدد';
        }

        const thankItem = document.createElement('div');
        thankItem.className = 'thanks-item';
        thankItem.innerHTML = `
            <h4>${employeeName}</h4>
            <p><strong>${typeText}</strong> - ${formatDate(thank.date)}</p>
            <p>${thank.reason || 'لا يوجد سبب محدد'}</p>
        `;

        container.appendChild(thankItem);
    });
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// إضافة شكر سريع (للاستخدام من صفحات أخرى)
function addQuickThanks(employeeId, type = 'appreciation') {
    // تعيين الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == employeeId);

    if (employee) {
        selectEmployee(employee);

        // تحديد نوع الشكر
        const typeCard = document.querySelector(`[data-type="${type}"]`);
        if (typeCard) {
            typeCard.click();
        }

        // التركيز على حقل السبب
        document.getElementById('thanksReason').focus();
    }
}

// إضافة إحصائيات سريعة للموظف المختار
function showEmployeeStats(employeeId) {
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employeeThanks = thanks.filter(t => t.employeeId == employeeId);

    const stats = {
        total: employeeThanks.length,
        appreciation: employeeThanks.filter(t => t.type === 'appreciation').length,
        ministerial: employeeThanks.filter(t => t.type === 'ministerial').length,
        presidential: employeeThanks.filter(t => t.type === 'presidential').length
    };

    // إضافة الإحصائيات إلى عرض الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.innerHTML += `<br><small>إجمالي التشكرات: ${stats.total} (جامعي: ${stats.appreciation}, وزاري: ${stats.ministerial}, رئاسي: ${stats.presidential})</small>`;
}

// تحديث عرض الموظف المختار مع الإحصائيات
function selectEmployee(employee) {
    selectedEmployeeId = employee.id;

    // تحديث حقل البحث
    document.getElementById('employeeSearch').value = employee.name;

    // إظهار الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.innerHTML = `${employee.name} - الرقم الوظيفي: ${employee.employeeNumber || employee.id}`;
    selectedDiv.style.display = 'flex';

    // إضافة الإحصائيات
    showEmployeeStats(employee.id);

    // عرض تأثير الشكر إذا كان نوع الشكر مختار
    if (selectedThanksType) {
        showThanksImpact();
    }

    // إخفاء القائمة المنسدلة
    document.getElementById('employeeDropdown').style.display = 'none';

    console.log('تم اختيار الموظف:', employee.name);
}

// عرض تأثير الشكر على الموظف
function showThanksImpact() {
    if (!selectedEmployeeId || !selectedThanksType) {
        document.getElementById('thanksPreview').style.display = 'none';
        return;
    }

    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == selectedEmployeeId);

    if (!employee) return;

    let impactText = '';
    let monthsAdvance = 0;

    switch (selectedThanksType) {
        case 'appreciation':
            impactText = 'شكر جامعي - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'ministerial':
            impactText = 'شكر وزاري - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'presidential':
            impactText = 'شكر رئاسي - تقديم ستة أشهر في العلاوة';
            monthsAdvance = 6;
            break;
    }

    // حساب التواريخ الجديدة
    let newAllowanceDate = '';
    let newPromotionDate = '';

    if (employee.nextAllowanceDate) {
        const currentDate = new Date(employee.nextAllowanceDate);
        currentDate.setMonth(currentDate.getMonth() - monthsAdvance);
        newAllowanceDate = currentDate.toISOString().split('T')[0];
    }

    if (employee.nextPromotionDate) {
        const currentDate = new Date(employee.nextPromotionDate);
        currentDate.setMonth(currentDate.getMonth() - monthsAdvance);
        newPromotionDate = currentDate.toISOString().split('T')[0];
    }

    const impactContainer = document.getElementById('thanksImpact');
    impactContainer.innerHTML = `
        <div class="impact-item">
            <strong>نوع التأثير:</strong> ${impactText}
        </div>
        ${newAllowanceDate ? `
        <div class="impact-item">
            <strong>تاريخ العلاوة الجديد:</strong> ${formatDate(newAllowanceDate)}
            <br><small>بدلاً من: ${formatDate(employee.nextAllowanceDate)}</small>
        </div>
        ` : ''}
        ${newPromotionDate ? `
        <div class="impact-item">
            <strong>تاريخ الترفيع الجديد:</strong> ${formatDate(newPromotionDate)}
            <br><small>بدلاً من: ${formatDate(employee.nextPromotionDate)}</small>
        </div>
        ` : ''}
    `;

    document.getElementById('thanksPreview').style.display = 'block';
}

// إخفاء معاينة التأثير
function hideThanksImpact() {
    document.getElementById('thanksPreview').style.display = 'none';
}

// تصدير الوظائف للاستخدام العام
window.addQuickThanks = addQuickThanks;
