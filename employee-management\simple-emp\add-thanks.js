// متغيرات عامة
let selectedEmployeeId = null;
let selectedEmployeeIds = []; // للشكر الجماعي
let selectedThanksType = null;
let selectedScope = null; // individual, group, all

// انتظر حتى يتم تحميل المستند بالكامل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة صفحة إضافة الشكر
    initAddThanksPage();
});

// تهيئة صفحة إضافة الشكر
function initAddThanksPage() {
    console.log('تهيئة صفحة إضافة الشكر...');

    // تهيئة البحث عن الموظفين
    initEmployeeSearch();

    // تهيئة اختيار نطاق الشكر
    initScopeSelection();

    // تهيئة اختيار نوع الشكر
    initThanksTypeSelection();

    // تهيئة النموذج
    initForm();

    // تحميل التشكرات الموجودة
    loadExistingThanks();

    // تهيئة البحث في التشكرات الموجودة
    initExistingThanksSearch();

    // تعيين التاريخ الحالي
    document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];

    console.log('تم تهيئة صفحة إضافة الشكر بنجاح');
}

// تهيئة البحث عن الموظفين
function initEmployeeSearch() {
    const searchInput = document.getElementById('employeeSearch');
    const dropdown = document.getElementById('employeeDropdown');

    if (!searchInput || !dropdown) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim().toLowerCase();

        if (searchTerm.length < 2) {
            dropdown.style.display = 'none';
            return;
        }

        // البحث في الموظفين
        const employees = JSON.parse(localStorage.getItem('employees') || '[]');
        const filteredEmployees = employees.filter(emp =>
            emp.name.toLowerCase().includes(searchTerm) ||
            emp.id.toString().includes(searchTerm) ||
            (emp.employeeNumber && emp.employeeNumber.toString().includes(searchTerm))
        );

        // عرض النتائج
        displayEmployeeOptions(filteredEmployees);
    });

    // إخفاء القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.style.display = 'none';
        }
    });
}

// عرض خيارات الموظفين
function displayEmployeeOptions(employees) {
    const dropdown = document.getElementById('employeeDropdown');

    if (employees.length === 0) {
        dropdown.innerHTML = '<div class="employee-option">لا توجد نتائج</div>';
        dropdown.style.display = 'block';
        return;
    }

    dropdown.innerHTML = '';

    employees.forEach(employee => {
        const option = document.createElement('div');
        option.className = 'employee-option';
        option.innerHTML = `
            <strong>${employee.name}</strong><br>
            <small>الرقم الوظيفي: ${employee.employeeNumber || employee.id} | ${employee.currentJobTitle || 'غير محدد'}</small>
        `;

        option.addEventListener('click', function() {
            selectEmployee(employee);
        });

        dropdown.appendChild(option);
    });

    dropdown.style.display = 'block';
}

// اختيار موظف
function selectEmployee(employee) {
    selectedEmployeeId = employee.id;

    // تحديث حقل البحث
    document.getElementById('employeeSearch').value = employee.name;

    // إظهار الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.textContent = `${employee.name} - الرقم الوظيفي: ${employee.employeeNumber || employee.id}`;
    selectedDiv.style.display = 'flex';

    // إخفاء القائمة المنسدلة
    document.getElementById('employeeDropdown').style.display = 'none';

    console.log('تم اختيار الموظف:', employee.name);
}

// تهيئة اختيار نوع الشكر
function initThanksTypeSelection() {
    const typeCards = document.querySelectorAll('.thanks-type-card');

    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            typeCards.forEach(c => c.classList.remove('selected'));

            // تحديد البطاقة الحالية
            this.classList.add('selected');

            // حفظ نوع الشكر المختار
            selectedThanksType = this.dataset.type;

            // تحديث الجهة المانحة تلقائياً
            updateIssuerField(selectedThanksType);

            // عرض تأثير الشكر إذا كان هناك موظف مختار
            if (selectedEmployeeId) {
                showThanksImpact();
            }

            console.log('تم اختيار نوع الشكر:', selectedThanksType);
        });
    });
}

// تحديث حقل الجهة المانحة
function updateIssuerField(type) {
    const issuerField = document.getElementById('thanksIssuer');

    switch (type) {
        case 'appreciation':
            issuerField.value = 'رئاسة الجامعة';
            break;
        case 'ministerial':
            issuerField.value = 'وزارة التعليم العالي والبحث العلمي';
            break;
        case 'presidential':
            issuerField.value = 'رئاسة مجلس الوزراء';
            break;
        default:
            issuerField.value = '';
    }
}

// تهيئة النموذج
function initForm() {
    const form = document.getElementById('addThanksForm');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitThanks();
        });
    }
}

// إرسال الشكر
function submitThanks() {
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return;
    }

    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الحفظ...';
    submitBtn.disabled = true;

    // تأخير قصير لإظهار التأثير البصري
    setTimeout(() => {
        // جمع البيانات الأساسية
        const baseData = {
            type: selectedThanksType,
            date: document.getElementById('thanksDate').value,
            number: document.getElementById('thanksNumber').value,
            issuer: document.getElementById('thanksIssuer').value,
            reason: document.getElementById('thanksReason').value,
            scope: selectedScope,
            createdAt: new Date().toISOString()
        };

        // تحديد الموظفين المستهدفين
        let targetEmployeeIds = [];
        if (selectedScope === 'individual') {
            targetEmployeeIds = [selectedEmployeeId];
        } else {
            targetEmployeeIds = selectedEmployeeIds;
        }

        // التحقق من وضع التعديل
        const submitBtn = document.querySelector('button[type="submit"]');
        const editId = submitBtn.dataset.editId;

        if (editId) {
            // وضع التحديث
            if (updateThanks(editId, thanksData)) {
                // تأثير النجاح
                document.querySelector('.thanks-form-card').classList.add('success-animation');

                // عرض رسالة نجاح
                showMessage('تم تحديث الشكر بنجاح وإعادة حساب تواريخ الموظف', 'success');

                // إعادة تعيين النموذج
                setTimeout(() => {
                    resetForm();
                    loadExistingThanks();
                }, 600);
            } else {
                showMessage('حدث خطأ أثناء تحديث الشكر', 'error');
            }
        } else {
            // وضع الإضافة
            if (saveMultipleThanks(baseData, targetEmployeeIds)) {
                // تأثير النجاح
                document.querySelector('.thanks-form-card').classList.add('success-animation');

                // عرض رسالة نجاح
                const employeeCount = targetEmployeeIds.length;
                const message = employeeCount === 1
                    ? 'تم إضافة الشكر بنجاح وتحديث تواريخ الموظف'
                    : `تم إضافة الشكر لـ ${employeeCount} موظف بنجاح وتحديث تواريخهم`;
                showMessage(message, 'success');

                // إعادة تعيين النموذج
                setTimeout(() => {
                    resetForm();
                    loadExistingThanks();
                }, 600);

            } else {
                showMessage('حدث خطأ أثناء حفظ الشكر', 'error');
            }
        }

        // إعادة تعيين الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // إزالة تأثير النجاح
        setTimeout(() => {
            document.querySelector('.thanks-form-card').classList.remove('success-animation');
        }, 600);

    }, 500);
}

// التحقق من صحة النموذج
function validateForm() {
    // إزالة تنسيق الأخطاء السابقة
    document.querySelectorAll('.form-validation').forEach(el => {
        el.classList.remove('form-validation');
    });

    let isValid = true;

    // التحقق من اختيار نطاق الشكر
    if (!selectedScope) {
        showMessage('يرجى اختيار نطاق الشكر (فردي، جماعي، أو عام)', 'error');
        document.querySelectorAll('.scope-card').forEach(card => {
            card.style.borderColor = '#ef4444';
        });
        isValid = false;
    }

    // التحقق من اختيار الموظفين حسب النطاق
    if (selectedScope === 'individual' && !selectedEmployeeId) {
        showMessage('يرجى اختيار موظف', 'error');
        const searchInput = document.getElementById('employeeSearch');
        if (searchInput) searchInput.classList.add('form-validation');
        isValid = false;
    } else if ((selectedScope === 'group' || selectedScope === 'all') && selectedEmployeeIds.length === 0) {
        showMessage('لا توجد موظفين مختارين للشكر الجماعي', 'error');
        isValid = false;
    }

    if (!selectedThanksType) {
        showMessage('يرجى اختيار نوع الشكر', 'error');
        document.querySelectorAll('.thanks-type-card').forEach(card => {
            card.style.borderColor = '#ef4444';
        });
        isValid = false;
    }

    const date = document.getElementById('thanksDate').value;
    if (!date) {
        showMessage('يرجى إدخال تاريخ الشكر', 'error');
        document.getElementById('thanksDate').classList.add('form-validation');
        isValid = false;
    } else {
        // التحقق من أن التاريخ ليس في المستقبل
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // نهاية اليوم

        if (selectedDate > today) {
            showMessage('لا يمكن أن يكون تاريخ الشكر في المستقبل', 'error');
            document.getElementById('thanksDate').classList.add('form-validation');
            isValid = false;
        }
    }

    return isValid;
}

// حفظ الشكر
function saveThanks(thanksData) {
    try {
        // الحصول على التشكرات الحالية
        let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

        // إنشاء معرف فريد
        const newId = thanks.length > 0 ? Math.max(...thanks.map(t => t.id || 0)) + 1 : 1;
        thanksData.id = newId;

        // إضافة الشكر الجديد
        thanks.push(thanksData);

        // حفظ في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // تحديث تواريخ الموظف باستخدام حاسبة التشكرات
        if (window.thanksCalculator) {
            window.thanksCalculator.updateNewDueDateBasedOnThanks(selectedEmployeeId);
            window.thanksCalculator.updateNextPromotionDateBasedOnThanks(selectedEmployeeId);
        }

        console.log('تم حفظ الشكر بنجاح:', thanksData);
        return true;

    } catch (error) {
        console.error('خطأ في حفظ الشكر:', error);
        return false;
    }
}

// حفظ شكر لعدة موظفين
function saveMultipleThanks(baseData, employeeIds) {
    try {
        let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        let maxId = thanks.length > 0 ? Math.max(...thanks.map(t => t.id || 0)) : 0;

        // إنشاء شكر منفصل لكل موظف
        employeeIds.forEach(employeeId => {
            maxId++;
            const thanksData = {
                ...baseData,
                id: maxId,
                employeeId: employeeId
            };
            thanks.push(thanksData);
        });

        // حفظ في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // تحديث تواريخ جميع الموظفين المتأثرين
        if (window.thanksCalculator) {
            employeeIds.forEach(employeeId => {
                window.thanksCalculator.updateNewDueDateBasedOnThanks(employeeId);
                window.thanksCalculator.updateNextPromotionDateBasedOnThanks(employeeId);
            });
        }

        console.log(`تم حفظ الشكر لـ ${employeeIds.length} موظف بنجاح`);
        return true;

    } catch (error) {
        console.error('خطأ في حفظ الشكر المتعدد:', error);
        return false;
    }
}

// تحديث شكر موجود
function updateThanks(thankId, thanksData) {
    try {
        let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
        const thankIndex = thanks.findIndex(t => t.id == thankId);

        if (thankIndex === -1) {
            console.error('لم يتم العثور على الشكر للتحديث');
            return false;
        }

        // الاحتفاظ بالمعرف الأصلي وتاريخ الإنشاء
        thanksData.id = parseInt(thankId);
        thanksData.createdAt = thanks[thankIndex].createdAt || new Date().toISOString();
        thanksData.updatedAt = new Date().toISOString();

        // استبدال الشكر القديم بالجديد
        thanks[thankIndex] = thanksData;

        // حفظ في التخزين المحلي
        localStorage.setItem('thanks', JSON.stringify(thanks));

        // تحديث تواريخ الموظف باستخدام حاسبة التشكرات
        if (window.thanksCalculator) {
            window.thanksCalculator.updateNewDueDateBasedOnThanks(thanksData.employeeId);
            window.thanksCalculator.updateNextPromotionDateBasedOnThanks(thanksData.employeeId);
        }

        console.log('تم تحديث الشكر بنجاح:', thanksData);
        return true;

    } catch (error) {
        console.error('خطأ في تحديث الشكر:', error);
        return false;
    }
}

// إعادة تعيين النموذج
function resetForm() {
    // إعادة تعيين المتغيرات
    selectedEmployeeId = null;
    selectedEmployeeIds = [];
    selectedThanksType = null;
    selectedScope = null;

    // إعادة تعيين حقول النموذج
    document.getElementById('employeeSearch').value = '';
    document.getElementById('selectedEmployee').style.display = 'none';
    document.getElementById('thanksNumber').value = '';
    document.getElementById('thanksIssuer').value = '';
    document.getElementById('thanksReason').value = '';

    // إزالة التحديد من بطاقات نطاق الشكر
    document.querySelectorAll('.scope-card').forEach(card => {
        card.classList.remove('selected');
        card.style.borderColor = ''; // إزالة لون الخطأ
    });

    // إزالة التحديد من بطاقات نوع الشكر
    document.querySelectorAll('.thanks-type-card').forEach(card => {
        card.classList.remove('selected');
        card.style.borderColor = ''; // إزالة لون الخطأ
    });

    // إخفاء جميع الأقسام
    document.getElementById('individualSection').style.display = 'none';
    document.getElementById('groupSection').style.display = 'none';
    hideSelectedEmployees();

    // إعادة تعيين قوائم المجموعة
    document.getElementById('groupType').value = '';
    document.getElementById('groupValue').innerHTML = '<option value="">اختر القيمة</option>';
    document.getElementById('groupValueContainer').style.display = 'none';

    // إخفاء معاينة التأثير
    hideThanksImpact();

    // تعيين التاريخ الحالي
    document.getElementById('thanksDate').value = new Date().toISOString().split('T')[0];

    // إعادة تعيين زر الحفظ
    const submitBtn = document.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الشكر';
    delete submitBtn.dataset.editId;

    console.log('تم إعادة تعيين النموذج');
}

// تم حذف وظيفة تحميل آخر التشكرات لأنها لم تعد مطلوبة

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// إضافة شكر سريع (للاستخدام من صفحات أخرى)
function addQuickThanks(employeeId, type = 'appreciation') {
    // تعيين الموظف
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == employeeId);

    if (employee) {
        selectEmployee(employee);

        // تحديد نوع الشكر
        const typeCard = document.querySelector(`[data-type="${type}"]`);
        if (typeCard) {
            typeCard.click();
        }

        // التركيز على حقل السبب
        document.getElementById('thanksReason').focus();
    }
}

// إضافة إحصائيات سريعة للموظف المختار
function showEmployeeStats(employeeId) {
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employeeThanks = thanks.filter(t => t.employeeId == employeeId);

    const stats = {
        total: employeeThanks.length,
        appreciation: employeeThanks.filter(t => t.type === 'appreciation').length,
        ministerial: employeeThanks.filter(t => t.type === 'ministerial').length,
        presidential: employeeThanks.filter(t => t.type === 'presidential').length
    };

    // إضافة الإحصائيات إلى عرض الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.innerHTML += `<br><small>إجمالي التشكرات: ${stats.total} (جامعي: ${stats.appreciation}, وزاري: ${stats.ministerial}, رئاسي: ${stats.presidential})</small>`;
}

// تحديث عرض الموظف المختار مع الإحصائيات
function selectEmployee(employee) {
    selectedEmployeeId = employee.id;

    // تحديث حقل البحث
    document.getElementById('employeeSearch').value = employee.name;

    // إظهار الموظف المختار
    const selectedDiv = document.getElementById('selectedEmployee');
    const nameSpan = document.getElementById('selectedEmployeeName');

    nameSpan.innerHTML = `${employee.name} - الرقم الوظيفي: ${employee.employeeNumber || employee.id}`;
    selectedDiv.style.display = 'flex';

    // إضافة الإحصائيات
    showEmployeeStats(employee.id);

    // عرض تأثير الشكر إذا كان نوع الشكر مختار
    if (selectedThanksType) {
        showThanksImpact();
    }

    // إخفاء القائمة المنسدلة
    document.getElementById('employeeDropdown').style.display = 'none';

    console.log('تم اختيار الموظف:', employee.name);
}

// عرض تأثير الشكر على الموظف
function showThanksImpact() {
    if (!selectedEmployeeId || !selectedThanksType) {
        document.getElementById('thanksPreview').style.display = 'none';
        return;
    }

    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == selectedEmployeeId);

    if (!employee) return;

    let impactText = '';
    let monthsAdvance = 0;

    switch (selectedThanksType) {
        case 'appreciation':
            impactText = 'شكر جامعي - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'ministerial':
            impactText = 'شكر وزاري - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'presidential':
            impactText = 'شكر رئاسي - تقديم ستة أشهر في العلاوة';
            monthsAdvance = 6;
            break;
    }

    // حساب التواريخ الجديدة
    let newAllowanceDate = '';
    let newPromotionDate = '';

    if (employee.nextAllowanceDate) {
        const currentDate = new Date(employee.nextAllowanceDate);
        currentDate.setMonth(currentDate.getMonth() - monthsAdvance);
        newAllowanceDate = currentDate.toISOString().split('T')[0];
    }

    if (employee.nextPromotionDate) {
        const currentDate = new Date(employee.nextPromotionDate);
        currentDate.setMonth(currentDate.getMonth() - monthsAdvance);
        newPromotionDate = currentDate.toISOString().split('T')[0];
    }

    const impactContainer = document.getElementById('thanksImpact');
    impactContainer.innerHTML = `
        <div class="impact-item">
            <strong>نوع التأثير:</strong> ${impactText}
        </div>
        ${newAllowanceDate ? `
        <div class="impact-item">
            <strong>تاريخ العلاوة الجديد:</strong> ${formatDate(newAllowanceDate)}
            <br><small>بدلاً من: ${formatDate(employee.nextAllowanceDate)}</small>
        </div>
        ` : ''}
        ${newPromotionDate ? `
        <div class="impact-item">
            <strong>تاريخ الترفيع الجديد:</strong> ${formatDate(newPromotionDate)}
            <br><small>بدلاً من: ${formatDate(employee.nextPromotionDate)}</small>
        </div>
        ` : ''}
    `;

    document.getElementById('thanksPreview').style.display = 'block';
}

// إخفاء معاينة التأثير
function hideThanksImpact() {
    document.getElementById('thanksPreview').style.display = 'none';
}

// تحميل التشكرات الموجودة
function loadExistingThanks() {
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    const container = document.getElementById('existingThanksList');

    if (thanks.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280; padding: 2rem;">لا توجد تشكرات مضافة بعد</p>';
        return;
    }

    // ترتيب التشكرات حسب التاريخ (الأحدث أولاً)
    thanks.sort((a, b) => new Date(b.date) - new Date(a.date));

    container.innerHTML = '';

    thanks.forEach(thank => {
        const employee = employees.find(emp => emp.id == thank.employeeId);
        const employeeName = employee ? employee.name : 'موظف غير موجود';

        let typeText = '';
        let typeClass = '';
        switch (thank.type) {
            case 'appreciation':
                typeText = 'شكر جامعي';
                typeClass = 'type-appreciation';
                break;
            case 'ministerial':
                typeText = 'شكر وزاري';
                typeClass = 'type-ministerial';
                break;
            case 'presidential':
                typeText = 'شكر رئاسي';
                typeClass = 'type-presidential';
                break;
            default:
                typeText = thank.type || 'غير محدد';
                typeClass = 'type-appreciation';
        }

        const thankItem = document.createElement('div');
        thankItem.className = 'existing-thank-item';
        thankItem.innerHTML = `
            <div class="thank-header">
                <div class="thank-employee-name">${employeeName}</div>
                <div class="thank-actions">
                    <button class="btn-edit" onclick="editThank(${thank.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-delete" onclick="deleteThank(${thank.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="thank-details">
                <span class="thank-type ${typeClass}">${typeText}</span><br>
                <strong>التاريخ:</strong> ${formatDate(thank.date)}<br>
                <strong>رقم الكتاب:</strong> ${thank.number || 'غير محدد'}<br>
                <strong>السبب:</strong> ${thank.reason || 'غير محدد'}
            </div>
        `;

        container.appendChild(thankItem);
    });
}

// تهيئة البحث في التشكرات الموجودة
function initExistingThanksSearch() {
    const searchInput = document.getElementById('searchExistingThanks');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            filterExistingThanks(searchTerm);
        });
    }
}

// فلترة التشكرات الموجودة
function filterExistingThanks(searchTerm) {
    const thankItems = document.querySelectorAll('.existing-thank-item');

    thankItems.forEach(item => {
        const text = item.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        item.style.display = isVisible ? 'block' : 'none';
    });
}

// تعديل شكر
function editThank(thankId) {
    const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const thank = thanks.find(t => t.id == thankId);

    if (!thank) {
        showMessage('لم يتم العثور على الشكر', 'error');
        return;
    }

    // ملء النموذج ببيانات الشكر
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const employee = employees.find(emp => emp.id == thank.employeeId);

    if (employee) {
        selectEmployee(employee);
    }

    // تحديد نوع الشكر
    selectedThanksType = thank.type;
    document.querySelectorAll('.thanks-type-card').forEach(card => {
        card.classList.remove('selected');
        if (card.dataset.type === thank.type) {
            card.classList.add('selected');
        }
    });

    // ملء باقي الحقول
    document.getElementById('thanksDate').value = thank.date;
    document.getElementById('thanksNumber').value = thank.number || '';
    document.getElementById('thanksReason').value = thank.reason || '';
    updateIssuerField(thank.type);

    // تغيير زر الحفظ إلى تحديث
    const submitBtn = document.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث الشكر';
    submitBtn.dataset.editId = thankId;

    // عرض معاينة التأثير
    if (selectedEmployeeId && selectedThanksType) {
        showThanksImpact();
    }

    // التمرير إلى النموذج
    document.querySelector('.thanks-form-card').scrollIntoView({ behavior: 'smooth' });

    showMessage('تم تحميل بيانات الشكر للتعديل', 'success');
}

// حذف شكر
function deleteThank(thankId) {
    if (!confirm('هل أنت متأكد من حذف هذا الشكر؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    let thanks = JSON.parse(localStorage.getItem('thanks') || '[]');
    const thankIndex = thanks.findIndex(t => t.id == thankId);

    if (thankIndex === -1) {
        showMessage('لم يتم العثور على الشكر', 'error');
        return;
    }

    const deletedThank = thanks[thankIndex];
    thanks.splice(thankIndex, 1);

    // حفظ البيانات المحدثة
    localStorage.setItem('thanks', JSON.stringify(thanks));

    // إعادة حساب تواريخ الموظف
    if (window.thanksCalculator && deletedThank.employeeId) {
        window.thanksCalculator.updateNewDueDateBasedOnThanks(deletedThank.employeeId);
        window.thanksCalculator.updateNextPromotionDateBasedOnThanks(deletedThank.employeeId);
    }

    // تحديث العرض
    loadExistingThanks();

    showMessage('تم حذف الشكر بنجاح وإعادة حساب تواريخ الموظف', 'success');
}

// تهيئة اختيار نطاق الشكر
function initScopeSelection() {
    const scopeCards = document.querySelectorAll('.scope-card');

    scopeCards.forEach(card => {
        card.addEventListener('click', function() {
            // إزالة التحديد من جميع البطاقات
            scopeCards.forEach(c => c.classList.remove('selected'));

            // تحديد البطاقة الحالية
            this.classList.add('selected');

            // حفظ نطاق الشكر المختار
            selectedScope = this.dataset.scope;

            // إظهار/إخفاء الأقسام المناسبة
            showScopeSection(selectedScope);

            console.log('تم اختيار نطاق الشكر:', selectedScope);
        });
    });

    // تهيئة اختيار المجموعة
    initGroupSelection();
}

// إظهار القسم المناسب حسب نطاق الشكر
function showScopeSection(scope) {
    // إخفاء جميع الأقسام
    document.getElementById('individualSection').style.display = 'none';
    document.getElementById('groupSection').style.display = 'none';

    // إعادة تعيين المتغيرات
    selectedEmployeeId = null;
    selectedEmployeeIds = [];

    // إظهار القسم المناسب
    switch (scope) {
        case 'individual':
            document.getElementById('individualSection').style.display = 'block';
            break;
        case 'group':
            document.getElementById('groupSection').style.display = 'block';
            break;
        case 'all':
            // تحديد جميع الموظفين
            selectAllEmployees();
            break;
    }

    // إخفاء معاينة التأثير
    hideThanksImpact();
}

// تهيئة اختيار المجموعة
function initGroupSelection() {
    const groupTypeSelect = document.getElementById('groupType');
    const groupValueContainer = document.getElementById('groupValueContainer');
    const groupValueSelect = document.getElementById('groupValue');

    if (groupTypeSelect) {
        groupTypeSelect.addEventListener('change', function() {
            const groupType = this.value;

            if (groupType) {
                groupValueContainer.style.display = 'block';
                populateGroupValues(groupType);
            } else {
                groupValueContainer.style.display = 'none';
                selectedEmployeeIds = [];
                hideSelectedEmployees();
            }
        });
    }

    if (groupValueSelect) {
        groupValueSelect.addEventListener('change', function() {
            const groupType = groupTypeSelect.value;
            const groupValue = this.value;

            if (groupType && groupValue) {
                selectEmployeesByGroup(groupType, groupValue);
            } else {
                selectedEmployeeIds = [];
                hideSelectedEmployees();
            }
        });
    }
}

// ملء قيم المجموعة حسب النوع
function populateGroupValues(groupType) {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    const groupValueSelect = document.getElementById('groupValue');

    groupValueSelect.innerHTML = '<option value="">اختر القيمة</option>';

    let values = new Set();

    switch (groupType) {
        case 'gender':
            values.add('ذكر');
            values.add('أنثى');
            break;
        case 'jobTitle':
            employees.forEach(emp => {
                if (emp.currentJobTitle) values.add(emp.currentJobTitle);
            });
            break;
        case 'workLocation':
            employees.forEach(emp => {
                if (emp.workLocation) values.add(emp.workLocation);
            });
            break;
        case 'educationLevel':
            employees.forEach(emp => {
                if (emp.educationLevel) values.add(emp.educationLevel);
            });
            break;
        case 'custom':
            // سيتم إضافة واجهة اختيار مخصص لاحقاً
            break;
    }

    // إضافة القيم إلى القائمة
    Array.from(values).sort().forEach(value => {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = value;
        groupValueSelect.appendChild(option);
    });
}

// اختيار الموظفين حسب المجموعة
function selectEmployeesByGroup(groupType, groupValue) {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');

    let filteredEmployees = [];

    switch (groupType) {
        case 'gender':
            filteredEmployees = employees.filter(emp => emp.gender === groupValue);
            break;
        case 'jobTitle':
            filteredEmployees = employees.filter(emp => emp.currentJobTitle === groupValue);
            break;
        case 'workLocation':
            filteredEmployees = employees.filter(emp => emp.workLocation === groupValue);
            break;
        case 'educationLevel':
            filteredEmployees = employees.filter(emp => emp.educationLevel === groupValue);
            break;
    }

    selectedEmployeeIds = filteredEmployees.map(emp => emp.id);
    showSelectedEmployees(filteredEmployees, `${getGroupTypeText(groupType)}: ${groupValue}`);
}

// اختيار جميع الموظفين
function selectAllEmployees() {
    const employees = JSON.parse(localStorage.getItem('employees') || '[]');
    selectedEmployeeIds = employees.map(emp => emp.id);
    showSelectedEmployees(employees, 'جميع الموظفين');
}

// عرض الموظفين المختارين
function showSelectedEmployees(employees, title) {
    const container = document.getElementById('selectedEmployees');
    const listContainer = document.getElementById('selectedEmployeesList');

    if (employees.length === 0) {
        hideSelectedEmployees();
        return;
    }

    container.style.display = 'block';

    listContainer.innerHTML = `
        <div class="employee-count">
            <i class="fas fa-users"></i> ${employees.length} موظف - ${title}
        </div>
        <div>
            ${employees.slice(0, 10).map(emp =>
                `<span class="employee-chip">${emp.name}</span>`
            ).join('')}
            ${employees.length > 10 ? `<span class="employee-chip">... و ${employees.length - 10} آخرين</span>` : ''}
        </div>
    `;

    // عرض معاينة التأثير للشكر الجماعي
    if (selectedThanksType) {
        showGroupThanksImpact(employees.length);
    }
}

// إخفاء الموظفين المختارين
function hideSelectedEmployees() {
    document.getElementById('selectedEmployees').style.display = 'none';
    hideThanksImpact();
}

// الحصول على نص نوع المجموعة
function getGroupTypeText(groupType) {
    const texts = {
        'gender': 'الجنس',
        'jobTitle': 'العنوان الوظيفي',
        'workLocation': 'موقع العمل',
        'educationLevel': 'المستوى التعليمي'
    };
    return texts[groupType] || groupType;
}

// عرض تأثير الشكر الجماعي
function showGroupThanksImpact(employeeCount) {
    if (!selectedThanksType) return;

    let impactText = '';
    let monthsAdvance = 0;

    switch (selectedThanksType) {
        case 'appreciation':
            impactText = 'شكر جامعي - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'ministerial':
            impactText = 'شكر وزاري - تقديم شهر واحد في العلاوة';
            monthsAdvance = 1;
            break;
        case 'presidential':
            impactText = 'شكر رئاسي - تقديم ستة أشهر في العلاوة';
            monthsAdvance = 6;
            break;
    }

    const impactContainer = document.getElementById('thanksImpact');
    impactContainer.innerHTML = `
        <div class="impact-item">
            <strong>نوع التأثير:</strong> ${impactText}
        </div>
        <div class="impact-item">
            <strong>عدد الموظفين المتأثرين:</strong> ${employeeCount} موظف
        </div>
        <div class="impact-item">
            <strong>التقديم في العلاوة:</strong> ${monthsAdvance} شهر لكل موظف
        </div>
        <div class="impact-item">
            <strong>إجمالي الأشهر المقدمة:</strong> ${employeeCount * monthsAdvance} شهر
        </div>
    `;

    document.getElementById('thanksPreview').style.display = 'block';
}

// تصدير الوظائف للاستخدام العام
window.addQuickThanks = addQuickThanks;
window.editThank = editThank;
window.deleteThank = deleteThank;
