<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد بيانات الموظفين من Excel - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .step-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .step-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .template-section {
            background: #e0f2fe;
            border: 1px solid #0288d1;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .template-btn {
            background: linear-gradient(135deg, #0288d1 0%, #0277bd 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .preview-table th,
        .preview-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .preview-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .preview-table tr:hover {
            background: #f8f9fa;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fef3c7;
            color: #92400e;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            height: 20px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .column-mapping {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .mapping-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .mapping-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .mapping-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-file-excel"></i> استيراد بيانات الموظفين من Excel</h1>
            <p>قم بتحميل ملف Excel يحتوي على بيانات الموظفين (مطابق لنموذج إضافة الموظف)</p>
        </div>

        <!-- تحميل القالب -->
        <div class="step-section">
            <h2><i class="fas fa-download"></i> الخطوة 1: تحميل القالب</h2>
            <div class="template-section">
                <h3>📋 قالب Excel مطابق لنموذج إضافة الموظف</h3>
                <p><strong>القالب مطابق تماماً لنموذج إضافة الموظف في الصورة:</strong></p>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li><strong>المعلومات الأساسية:</strong> الاسم الثلاثي، الاسم الرباعي، اللقب الوظيفي</li>
                    <li><strong>التحصيل الدراسي:</strong> التحصيل الدراسي، العنوان الوظيفي، اللقب الوظيفي</li>
                    <li><strong>البيانات الوظيفية:</strong> مجموع النقاط، التخصص، تاريخ الولادة، تاريخ التعيين، تاريخ المباشرة</li>
                    <li><strong>العلاوات:</strong> الراتب الحالي، المرحلة الحالية، الدرجة الحالية، مدة الخدمة، تواريخ الاستحقاق</li>
                </ul>
                <p><strong>✅ مطابق 100%</strong> لجميع حقول النموذج في الصورة</p>
                <p><strong>✅ يشمل 3 موظفين</strong> كأمثلة بالبيانات المطابقة للنموذج</p>
                <button class="template-btn" onclick="downloadTemplate()">
                    <i class="fas fa-download"></i>
                    تحميل القالب المطابق للنموذج
                </button>
            </div>
        </div>

        <!-- رفع الملف -->
        <div class="step-section">
            <h2><i class="fas fa-upload"></i> الخطوة 2: رفع ملف Excel</h2>
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-cloud-upload-alt" style="font-size: 3em; color: #667eea; margin-bottom: 20px;"></i>
                <h3>اسحب ملف Excel هنا أو انقر للاختيار</h3>
                <p>يدعم ملفات .xlsx و .xls</p>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" onchange="handleFile(this.files[0])">
                <button class="upload-btn">
                    <i class="fas fa-folder-open"></i>
                    اختيار ملف
                </button>
            </div>
            <div id="fileInfo" style="display: none; margin-top: 15px;"></div>
        </div>

        <!-- معاينة البيانات -->
        <div class="step-section" id="previewSection" style="display: none;">
            <h2><i class="fas fa-eye"></i> الخطوة 3: معاينة البيانات</h2>
            <div id="columnMapping" class="column-mapping"></div>
            <div id="previewContainer"></div>
            <div id="importResult"></div>
            <button class="upload-btn" onclick="importData()" id="importBtn" style="display: none;">
                <i class="fas fa-database"></i>
                استيراد البيانات
            </button>
        </div>
    </div>

    <script>
        let excelData = [];
        let columnHeaders = [];

        // إعداد السحب والإفلات
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // تحميل القالب الشامل
        function downloadTemplate() {
            const templateData = [
                // رؤوس الأعمدة - مطابقة تماماً للنموذج في الصورة
                [
                    // المعلومات الأساسية (من النموذج)
                    'الاسم الثلاثي', 'الاسم الرباعي', 'اللقب الوظيفي',

                    // التحصيل الدراسي (من النموذج)
                    'التحصيل الدراسي', 'العنوان الوظيفي', 'اللقب الوظيفي',

                    // البيانات الوظيفية (من النموذج)
                    'مجموع النقاط', 'التخصص', 'تاريخ الولادة',
                    'تاريخ التعيين', 'تاريخ المباشرة',

                    // العلاوات (من النموذج)
                    'الراتب الحالي', 'المرحلة الحالية', 'الدرجة الحالية',
                    'مدة الخدمة بالدرجة الحالية', 'تاريخ الاستحقاق الحالي',
                    'تاريخ آخر ترفيع', 'تاريخ الاستحقاق القادم'
                ],

                // بيانات تجريبية - الموظف الأول (مطابقة للنموذج في الصورة)
                [
                    // المعلومات الأساسية
                    'أحمد محمد علي', 'أحمد محمد علي حسن', 'مدرس',

                    // التحصيل الدراسي
                    'بكالوريوس', 'مدرس مساعد', 'مدرس',

                    // البيانات الوظيفية
                    '85', 'هندسة الحاسوب', '1985-03-20',
                    '2015-01-15', '2015-02-01',

                    // العلاوات
                    '850000', '3', '5',
                    '5 سنوات', '2025-01-15',
                    '2020-01-15', '2025-01-15'
                ],

                // بيانات تجريبية - الموظف الثاني (مطابقة للنموذج في الصورة)
                [
                    // المعلومات الأساسية
                    'فاطمة أحمد حسن', 'فاطمة أحمد حسن محمود', 'مدرس مساعد',

                    // التحصيل الدراسي
                    'ماجستير', 'مدرس', 'مدرس مساعد',

                    // البيانات الوظيفية
                    '92', 'الرياضيات', '1990-07-12',
                    '2018-06-01', '2018-07-01',

                    // العلاوات
                    '720000', '2', '4',
                    '3 سنوات', '2025-06-01',
                    '2021-06-01', '2025-06-01'
                ],

                // بيانات تجريبية - الموظف الثالث (مطابقة للنموذج في الصورة)
                [
                    // المعلومات الأساسية
                    'محمد خالد عبدالله', 'محمد خالد عبدالله صالح', 'أستاذ مساعد',

                    // التحصيل الدراسي
                    'دكتوراه', 'أستاذ مساعد', 'أستاذ مساعد',

                    // البيانات الوظيفية
                    '98', 'الفيزياء', '1982-11-05',
                    '2012-09-10', '2012-10-01',

                    // العلاوات
                    '950000', '4', '6',
                    '8 سنوات', '2024-09-10',
                    '2019-09-10', '2024-09-10'
                ]
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);

            // تنسيق العرض
            const range = XLSX.utils.decode_range(ws['!ref']);
            ws['!cols'] = [];
            for (let i = 0; i <= range.e.c; i++) {
                ws['!cols'][i] = { wch: 20 }; // عرض الأعمدة
            }

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'ملف_الموظف_الشامل');
            XLSX.writeFile(wb, 'قالب_ملف_الموظف_مطابق_للنظام.xlsx');
        }

        // معالجة الملف المرفوع
        function handleFile(file) {
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result">
                    <h4><i class="fas fa-file-excel"></i> معلومات الملف:</h4>
                    <p><strong>الاسم:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>النوع:</strong> ${file.type}</p>
                    <div class="progress">
                        <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                    </div>
                </div>
            `;

            const reader = new FileReader();
            
            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percentLoaded = Math.round((e.loaded / e.total) * 100);
                    const progressBar = document.getElementById('progressBar');
                    progressBar.style.width = percentLoaded + '%';
                    progressBar.textContent = percentLoaded + '%';
                }
            };

            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    if (jsonData.length > 0) {
                        columnHeaders = jsonData[0];
                        excelData = jsonData.slice(1);
                        showPreview();
                    } else {
                        showError('الملف فارغ أو لا يحتوي على بيانات');
                    }
                } catch (error) {
                    showError('خطأ في قراءة الملف: ' + error.message);
                }
            };

            reader.onerror = function() {
                showError('خطأ في تحميل الملف');
            };

            reader.readAsArrayBuffer(file);
        }

        // عرض معاينة البيانات
        function showPreview() {
            const previewSection = document.getElementById('previewSection');
            const columnMapping = document.getElementById('columnMapping');
            const previewContainer = document.getElementById('previewContainer');
            
            previewSection.style.display = 'block';

            // إنشاء خريطة الأعمدة - مطابقة تماماً للنموذج في الصورة
            const systemFields = [
                // المعلومات الأساسية (من النموذج)
                { key: 'name', label: 'الاسم الثلاثي', required: true },
                { key: 'fullName', label: 'الاسم الرباعي', required: true },
                { key: 'jobTitle', label: 'اللقب الوظيفي', required: true },

                // التحصيل الدراسي (من النموذج)
                { key: 'educationLevel', label: 'التحصيل الدراسي' },
                { key: 'currentJobTitle', label: 'العنوان الوظيفي' },
                { key: 'jobTitleRepeat', label: 'اللقب الوظيفي' },

                // البيانات الوظيفية (من النموذج)
                { key: 'totalPoints', label: 'مجموع النقاط' },
                { key: 'specialization', label: 'التخصص' },
                { key: 'birthDate', label: 'تاريخ الولادة', required: true },
                { key: 'appointmentDate', label: 'تاريخ التعيين', required: true },
                { key: 'startDate', label: 'تاريخ المباشرة' },

                // العلاوات (من النموذج)
                { key: 'currentSalary', label: 'الراتب الحالي', required: true },
                { key: 'currentStage', label: 'المرحلة الحالية', required: true },
                { key: 'currentDegree', label: 'الدرجة الحالية', required: true },
                { key: 'servicePeriod', label: 'مدة الخدمة بالدرجة الحالية' },
                { key: 'currentDueDate', label: 'تاريخ الاستحقاق الحالي' },
                { key: 'lastPromotionDate', label: 'تاريخ آخر ترفيع' },
                { key: 'nextDueDate', label: 'تاريخ الاستحقاق القادم' }
            ];

            let mappingHTML = '<h3>ربط الأعمدة: <span style="color: #ef4444;">*</span> = حقول إجبارية</h3>';

            // تجميع الحقول حسب الفئة (مطابق للنموذج في الصورة)
            const fieldGroups = {
                'المعلومات الأساسية': systemFields.slice(0, 3),
                'التحصيل الدراسي': systemFields.slice(3, 6),
                'البيانات الوظيفية': systemFields.slice(6, 11),
                'العلاوات': systemFields.slice(11)
            };

            Object.keys(fieldGroups).forEach(groupName => {
                mappingHTML += `<h4 style="color: #667eea; margin-top: 20px; margin-bottom: 10px;">${groupName}</h4>`;
                mappingHTML += '<div class="column-mapping">';

                fieldGroups[groupName].forEach(field => {
                    const requiredMark = field.required ? '<span style="color: #ef4444;">*</span>' : '';
                    mappingHTML += `
                        <div class="mapping-item">
                            <label>${field.label} ${requiredMark}:</label>
                            <select id="map_${field.key}">
                                <option value="">-- اختر العمود --</option>
                                ${columnHeaders.map((header, index) => {
                                    // محاولة الربط التلقائي الذكي
                                    const isMatch = header.includes(field.label) ||
                                                  header.includes(field.label.split(' ')[0]) ||
                                                  (field.key === 'fullName' && (header.includes('اسم') || header.includes('الاسم'))) ||
                                                  (field.key === 'employeeNumber' && (header.includes('رقم') && header.includes('وظيفي'))) ||
                                                  (field.key === 'currentSalary' && header.includes('راتب')) ||
                                                  (field.key === 'currentDegree' && header.includes('درجة')) ||
                                                  (field.key === 'currentStage' && header.includes('مرحلة'));
                                    return `<option value="${index}" ${isMatch ? 'selected' : ''}>${header}</option>`;
                                }).join('')}
                            </select>
                        </div>
                    `;
                });

                mappingHTML += '</div>';
            });

            columnMapping.innerHTML = mappingHTML;

            // عرض معاينة البيانات
            let previewHTML = `
                <h3>معاينة البيانات (أول 5 صفوف):</h3>
                <table class="preview-table">
                    <thead>
                        <tr>${columnHeaders.map(header => `<th>${header}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
                        ${excelData.slice(0, 5).map(row => 
                            `<tr>${row.map(cell => `<td>${cell || '-'}</td>`).join('')}</tr>`
                        ).join('')}
                    </tbody>
                </table>
                <p style="margin-top: 10px; color: #666;">
                    <strong>إجمالي الصفوف:</strong> ${excelData.length}
                </p>
            `;

            previewContainer.innerHTML = previewHTML;
            document.getElementById('importBtn').style.display = 'inline-block';
        }

        // استيراد البيانات
        function importData() {
            const resultDiv = document.getElementById('importResult');
            
            try {
                // جمع خريطة الأعمدة
                const mapping = {};
                const allSystemFields = [
                    'fullName', 'employeeNumber', 'nationalId', 'birthDate', 'birthPlace', 'nationality', 'gender', 'maritalStatus', 'childrenCount',
                    'mobilePhone', 'homePhone', 'email', 'currentAddress', 'permanentAddress',
                    'jobTitle', 'jobDescription', 'workLocation', 'department', 'hireDate', 'employmentType', 'directSupervisor',
                    'currentSalary', 'currentDegree', 'currentStage', 'lastPromotionDate', 'nextPromotionDate', 'lastAllowanceDate', 'nextAllowanceDate', 'seniority',
                    'highestDegree', 'specialization', 'university', 'graduationYear', 'gpa',
                    'languages', 'skills', 'trainingCourses', 'previousExperience', 'notes'
                ];

                allSystemFields.forEach(field => {
                    const select = document.getElementById(`map_${field}`);
                    if (select && select.value !== '') {
                        mapping[field] = parseInt(select.value);
                    }
                });

                // التحقق من الحقول المطلوبة (مطابق للنموذج)
                const requiredFields = ['name', 'fullName', 'jobTitle', 'birthDate', 'appointmentDate', 'currentSalary', 'currentDegree', 'currentStage'];
                const missingFields = [];

                requiredFields.forEach(field => {
                    if (!mapping[field]) {
                        const fieldLabel = systemFields.find(f => f.key === field)?.label || field;
                        missingFields.push(fieldLabel);
                    }
                });

                if (missingFields.length > 0) {
                    throw new Error('يجب ربط الحقول الإجبارية التالية: ' + missingFields.join(', '));
                }

                // تحويل البيانات
                const employees = [];
                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                excelData.forEach((row, index) => {
                    try {
                        // التحقق من الحقول الإجبارية (مطابق للنموذج)
                        if (!row[mapping.name] || row[mapping.name].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الاسم الثلاثي مطلوب`);
                            errorCount++;
                            return;
                        }

                        if (!row[mapping.fullName] || row[mapping.fullName].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الاسم الرباعي مطلوب`);
                            errorCount++;
                            return;
                        }

                        if (!row[mapping.jobTitle] || row[mapping.jobTitle].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: اللقب الوظيفي مطلوب`);
                            errorCount++;
                            return;
                        }

                        // إنشاء كائن الموظف (مطابق للنموذج في الصورة)
                        const employee = {
                            // معرف فريد
                            id: Date.now() + index,
                            createdAt: new Date().toISOString(),

                            // المعلومات الأساسية (من النموذج)
                            name: row[mapping.name] || '',
                            fullName: row[mapping.fullName] || '',
                            jobTitle: row[mapping.jobTitle] || '',

                            // التحصيل الدراسي (من النموذج)
                            educationLevel: row[mapping.educationLevel] || '',
                            currentJobTitle: row[mapping.currentJobTitle] || '',
                            jobTitleRepeat: row[mapping.jobTitleRepeat] || '',

                            // البيانات الوظيفية (من النموذج)
                            totalPoints: row[mapping.totalPoints] || '',
                            specialization: row[mapping.specialization] || '',
                            birthDate: formatExcelDate(row[mapping.birthDate]) || '',
                            appointmentDate: formatExcelDate(row[mapping.appointmentDate]) || '',
                            startDate: formatExcelDate(row[mapping.startDate]) || '',

                            // العلاوات (من النموذج)
                            currentSalary: row[mapping.currentSalary] || '0',
                            currentStage: row[mapping.currentStage] || 1,
                            currentDegree: row[mapping.currentDegree] || 1,
                            servicePeriod: row[mapping.servicePeriod] || '',
                            currentDueDate: formatExcelDate(row[mapping.currentDueDate]) || '',
                            lastPromotionDate: formatExcelDate(row[mapping.lastPromotionDate]) || '',
                            nextDueDate: formatExcelDate(row[mapping.nextDueDate]) || '',

                            // للتوافق مع النظام القديم
                            employeeNumber: row[mapping.name] || '',
                            hireDate: formatExcelDate(row[mapping.appointmentDate]) || '',
                            seniority: calculateSeniority(formatExcelDate(row[mapping.appointmentDate]))
                        };

                        employees.push(employee);
                        successCount++;
                    } catch (error) {
                        errors.push(`الصف ${index + 2}: ${error.message}`);
                        errorCount++;
                    }
                });

                // حفظ البيانات
                if (employees.length > 0) {
                    const existingEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
                    const allEmployees = [...existingEmployees, ...employees];
                    localStorage.setItem('employees', JSON.stringify(allEmployees));

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم استيراد البيانات بنجاح!</h4>
                        <p><strong>تم استيراد:</strong> ${successCount} موظف</p>
                        ${errorCount > 0 ? `<p><strong>أخطاء:</strong> ${errorCount}</p>` : ''}
                        ${errors.length > 0 ? `<details><summary>عرض الأخطاء</summary><ul>${errors.map(error => `<li>${error}</li>`).join('')}</ul></details>` : ''}
                        <p style="margin-top: 15px;">
                            <a href="employees-list.html" class="template-btn">
                                <i class="fas fa-users"></i>
                                عرض قائمة الموظفين
                            </a>
                        </p>
                    `;
                } else {
                    throw new Error('لم يتم استيراد أي موظف');
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الاستيراد: ${error.message}</h4>`;
            }
        }

        // تنسيق تاريخ Excel
        function formatExcelDate(excelDate) {
            if (!excelDate) return null;
            
            // إذا كان التاريخ رقم (Excel date serial)
            if (typeof excelDate === 'number') {
                const date = new Date((excelDate - 25569) * 86400 * 1000);
                return date.toISOString().split('T')[0];
            }
            
            // إذا كان التاريخ نص
            if (typeof excelDate === 'string') {
                const date = new Date(excelDate);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            }
            
            return null;
        }

        // حساب سنوات الخدمة
        function calculateSeniority(hireDate) {
            if (!hireDate) return 0;

            const hire = new Date(hireDate);
            const today = new Date();
            const years = Math.floor((today - hire) / (365.25 * 24 * 60 * 60 * 1000));

            return years >= 0 ? years : 0;
        }

        // عرض خطأ
        function showError(message) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result error">
                    <h4>❌ خطأ:</h4>
                    <p>${message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
