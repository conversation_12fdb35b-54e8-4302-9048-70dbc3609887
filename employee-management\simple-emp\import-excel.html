<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد بيانات الموظفين من Excel - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }

        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 2.2em;
        }

        .step-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .step-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .template-section {
            background: #e0f2fe;
            border: 1px solid #0288d1;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .template-btn {
            background: linear-gradient(135deg, #0288d1 0%, #0277bd 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .preview-table th,
        .preview-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .preview-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .preview-table tr:hover {
            background: #f8f9fa;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fef3c7;
            color: #92400e;
        }

        .progress {
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            height: 20px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            font-size: 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .column-mapping {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .mapping-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .mapping-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .mapping-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- زر العودة -->
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>

        <!-- العنوان -->
        <div class="header">
            <h1><i class="fas fa-file-excel"></i> استيراد بيانات الموظفين من Excel</h1>
            <p>قم بتحميل ملف Excel لاستيراد بيانات الموظفين إلى النظام</p>
        </div>

        <!-- تحميل القالب -->
        <div class="step-section">
            <h2><i class="fas fa-download"></i> الخطوة 1: تحميل القالب</h2>
            <div class="template-section">
                <h3>📋 قالب Excel مطابق لنموذج إضافة الموظف</h3>
                <p><strong>القالب مطابق تماماً لنموذج إضافة الموظف في النظام:</strong></p>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li><strong>البيانات الشخصية:</strong> الاسم الكامل، الرقم الوظيفي، رقم الهوية، تاريخ الميلاد، إلخ</li>
                    <li><strong>معلومات الاتصال:</strong> الهاتف المحمول، الثابت، البريد الإلكتروني، العناوين</li>
                    <li><strong>البيانات الوظيفية:</strong> العنوان الوظيفي، الوصف، موقع العمل، القسم، إلخ</li>
                    <li><strong>الراتب والدرجة:</strong> الراتب الأساسي، الدرجة، المرحلة، تواريخ الترفيعات والعلاوات</li>
                    <li><strong>التحصيل الدراسي:</strong> المؤهل العلمي، التخصص، الجامعة، سنة التخرج، المعدل</li>
                    <li><strong>معلومات إضافية:</strong> اللغات، المهارات، الدورات، الخبرات، الملاحظات</li>
                </ul>
                <p><strong>✅ مطابق 100%</strong> لجميع حقول نموذج إضافة الموظف</p>
                <p><strong>✅ يشمل 3 موظفين</strong> كأمثلة بجميع البيانات</p>
                <button class="template-btn" onclick="downloadTemplate()">
                    <i class="fas fa-download"></i>
                    تحميل القالب المطابق للنظام
                </button>
            </div>
        </div>

        <!-- رفع الملف -->
        <div class="step-section">
            <h2><i class="fas fa-upload"></i> الخطوة 2: رفع ملف Excel</h2>
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-cloud-upload-alt" style="font-size: 3em; color: #667eea; margin-bottom: 20px;"></i>
                <h3>اسحب ملف Excel هنا أو انقر للاختيار</h3>
                <p>يدعم ملفات .xlsx و .xls</p>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" onchange="handleFile(this.files[0])">
                <button class="upload-btn">
                    <i class="fas fa-folder-open"></i>
                    اختيار ملف
                </button>
            </div>
            <div id="fileInfo" style="display: none; margin-top: 15px;"></div>
        </div>

        <!-- معاينة البيانات -->
        <div class="step-section" id="previewSection" style="display: none;">
            <h2><i class="fas fa-eye"></i> الخطوة 3: معاينة البيانات</h2>
            <div id="columnMapping" class="column-mapping"></div>
            <div id="previewContainer"></div>
            <div id="importResult"></div>
            <button class="upload-btn" onclick="importData()" id="importBtn" style="display: none;">
                <i class="fas fa-database"></i>
                استيراد البيانات
            </button>
        </div>
    </div>

    <script>
        let excelData = [];
        let columnHeaders = [];

        // إعداد السحب والإفلات
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // تحميل القالب الشامل
        function downloadTemplate() {
            const templateData = [
                // رؤوس الأعمدة - مطابقة تماماً لنموذج إضافة الموظف
                [
                    // البيانات الشخصية (مطابقة للنموذج)
                    'الاسم الكامل', 'الرقم الوظيفي', 'رقم الهوية الوطنية', 'تاريخ الميلاد (ميلادي)', 'مكان الميلاد',
                    'الجنسية', 'الجنس', 'الحالة الاجتماعية', 'عدد الأطفال',

                    // معلومات الاتصال (مطابقة للنموذج)
                    'رقم الهاتف المحمول', 'رقم الهاتف الثابت', 'البريد الإلكتروني', 'العنوان الحالي', 'العنوان الدائم',

                    // البيانات الوظيفية (مطابقة للنموذج)
                    'العنوان الوظيفي', 'الوصف الوظيفي', 'موقع العمل', 'القسم/الوحدة', 'تاريخ التوظيف (ميلادي)',
                    'نوع التوظيف', 'المرجع المباشر',

                    // الراتب والدرجة الوظيفية (مطابقة للنموذج)
                    'الراتب الأساسي الحالي', 'الدرجة الحالية', 'المرحلة الحالية', 'تاريخ آخر ترفيع (ميلادي)',
                    'تاريخ استحقاق الترفيع القادم (ميلادي)', 'تاريخ آخر علاوة (ميلادي)', 'تاريخ استحقاق العلاوة القادمة (ميلادي)', 'سنوات الخدمة',

                    // التحصيل الدراسي (مطابقة للنموذج)
                    'أعلى مؤهل علمي', 'التخصص', 'الجامعة/المؤسسة التعليمية', 'سنة التخرج', 'المعدل/التقدير',

                    // معلومات إضافية (مطابقة للنموذج)
                    'اللغات', 'المهارات', 'الدورات التدريبية', 'الخبرات السابقة', 'ملاحظات'
                ],

                // بيانات تجريبية - الموظف الأول (مطابقة للنموذج)
                [
                    // البيانات الشخصية
                    'أحمد محمد علي حسن', '12345', '123456789012', '1985-03-20', 'بغداد',
                    'عراقي', 'ذكر', 'متزوج', '2',

                    // معلومات الاتصال
                    '07901234567', '07801234567', '<EMAIL>', 'بغداد - الكرادة - شارع الرئيسي', 'بغداد - الكرادة - شارع الرئيسي',

                    // البيانات الوظيفية
                    'مهندس برمجيات أول', 'تدريسي', 'المقر الرئيسي', 'قسم تقنية المعلومات', '2015-01-15',
                    'دائم', 'مدير قسم تقنية المعلومات',

                    // الراتب والدرجة الوظيفية
                    '850000', '5', '3', '2020-01-15', '2025-01-15',
                    '2024-01-15', '2025-01-15', '10',

                    // التحصيل الدراسي
                    'بكالوريوس', 'هندسة البرمجيات', 'جامعة بغداد', '2014', 'جيد جداً',

                    // معلومات إضافية
                    'العربية، الإنجليزية', 'البرمجة، إدارة المشاريع، قواعد البيانات', 'دورة إدارة المشاريع، دورة الأمن السيبراني', 'مطور في شركة تقنية لمدة 3 سنوات', 'موظف متميز ومجتهد'
                ],

                // بيانات تجريبية - الموظف الثاني (مطابقة للنموذج)
                [
                    // البيانات الشخصية
                    'فاطمة أحمد حسن محمود', '12346', '123456789013', '1990-07-12', 'البصرة',
                    'عراقي', 'أنثى', 'متزوج', '1',

                    // معلومات الاتصال
                    '07901234568', '07801234568', '<EMAIL>', 'البصرة - الجمعيات - شارع الكورنيش', 'البصرة - الجمعيات - شارع الكورنيش',

                    // البيانات الوظيفية
                    'محاسب أول', 'إداري', 'الفرع الأول', 'قسم المحاسبة', '2018-06-01',
                    'دائم', 'مدير المحاسبة',

                    // الراتب والدرجة الوظيفية
                    '720000', '4', '2', '2021-06-01', '2026-06-01',
                    '2024-06-01', '2025-06-01', '7',

                    // التحصيل الدراسي
                    'بكالوريوس', 'المحاسبة', 'جامعة البصرة', '2017', 'امتياز',

                    // معلومات إضافية
                    'العربية، الإنجليزية', 'المحاسبة، Excel المتقدم، التدقيق المالي', 'دورة المحاسبة المتقدمة، دورة التدقيق الداخلي', 'محاسب في شركة خاصة لمدة سنتين', 'دقيقة في العمل ومنظمة'
                ],

                // بيانات تجريبية - الموظف الثالث (مطابقة للنموذج)
                [
                    // البيانات الشخصية
                    'محمد خالد عبدالله صالح', '12347', '123456789014', '1982-11-05', 'الموصل',
                    'عراقي', 'ذكر', 'متزوج', '3',

                    // معلومات الاتصال
                    '07901234569', '07801234569', '<EMAIL>', 'الموصل - الجامعة - شارع الجامعة', 'الموصل - الجامعة - شارع الجامعة',

                    // البيانات الوظيفية
                    'مدير مبيعات', 'إداري', 'الفرع الثاني', 'قسم المبيعات', '2012-09-10',
                    'دائم', 'المدير العام',

                    // الراتب والدرجة الوظيفية
                    '950000', '6', '4', '2019-09-10', '2024-09-10',
                    '2024-09-10', '2025-09-10', '13',

                    // التحصيل الدراسي
                    'ماجستير', 'إدارة الأعمال', 'جامعة الموصل', '2011', 'جيد جداً',

                    // معلومات إضافية
                    'العربية، الإنجليزية، التركية', 'إدارة المبيعات، التسويق، التفاوض', 'دورة إدارة المبيعات، دورة التسويق الرقمي', 'مدير مبيعات في عدة شركات لمدة 8 سنوات', 'قائد فريق ممتاز ولديه خبرة واسعة'
                ]
            ];

            const ws = XLSX.utils.aoa_to_sheet(templateData);

            // تنسيق العرض
            const range = XLSX.utils.decode_range(ws['!ref']);
            ws['!cols'] = [];
            for (let i = 0; i <= range.e.c; i++) {
                ws['!cols'][i] = { wch: 20 }; // عرض الأعمدة
            }

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'ملف_الموظف_الشامل');
            XLSX.writeFile(wb, 'قالب_ملف_الموظف_مطابق_للنظام.xlsx');
        }

        // معالجة الملف المرفوع
        function handleFile(file) {
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result">
                    <h4><i class="fas fa-file-excel"></i> معلومات الملف:</h4>
                    <p><strong>الاسم:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>النوع:</strong> ${file.type}</p>
                    <div class="progress">
                        <div class="progress-bar" id="progressBar" style="width: 0%">0%</div>
                    </div>
                </div>
            `;

            const reader = new FileReader();
            
            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percentLoaded = Math.round((e.loaded / e.total) * 100);
                    const progressBar = document.getElementById('progressBar');
                    progressBar.style.width = percentLoaded + '%';
                    progressBar.textContent = percentLoaded + '%';
                }
            };

            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                    
                    if (jsonData.length > 0) {
                        columnHeaders = jsonData[0];
                        excelData = jsonData.slice(1);
                        showPreview();
                    } else {
                        showError('الملف فارغ أو لا يحتوي على بيانات');
                    }
                } catch (error) {
                    showError('خطأ في قراءة الملف: ' + error.message);
                }
            };

            reader.onerror = function() {
                showError('خطأ في تحميل الملف');
            };

            reader.readAsArrayBuffer(file);
        }

        // عرض معاينة البيانات
        function showPreview() {
            const previewSection = document.getElementById('previewSection');
            const columnMapping = document.getElementById('columnMapping');
            const previewContainer = document.getElementById('previewContainer');
            
            previewSection.style.display = 'block';

            // إنشاء خريطة الأعمدة - مطابقة تماماً لنموذج إضافة الموظف
            const systemFields = [
                // البيانات الشخصية (مطابقة للنموذج)
                { key: 'fullName', label: 'الاسم الكامل', required: true },
                { key: 'employeeNumber', label: 'الرقم الوظيفي', required: true },
                { key: 'nationalId', label: 'رقم الهوية الوطنية' },
                { key: 'birthDate', label: 'تاريخ الميلاد (ميلادي)' },
                { key: 'birthPlace', label: 'مكان الميلاد' },
                { key: 'nationality', label: 'الجنسية' },
                { key: 'gender', label: 'الجنس' },
                { key: 'maritalStatus', label: 'الحالة الاجتماعية' },
                { key: 'childrenCount', label: 'عدد الأطفال' },

                // معلومات الاتصال (مطابقة للنموذج)
                { key: 'mobilePhone', label: 'رقم الهاتف المحمول' },
                { key: 'homePhone', label: 'رقم الهاتف الثابت' },
                { key: 'email', label: 'البريد الإلكتروني' },
                { key: 'currentAddress', label: 'العنوان الحالي' },
                { key: 'permanentAddress', label: 'العنوان الدائم' },

                // البيانات الوظيفية (مطابقة للنموذج)
                { key: 'jobTitle', label: 'العنوان الوظيفي', required: true },
                { key: 'jobDescription', label: 'الوصف الوظيفي' },
                { key: 'workLocation', label: 'موقع العمل', required: true },
                { key: 'department', label: 'القسم/الوحدة' },
                { key: 'hireDate', label: 'تاريخ التوظيف (ميلادي)', required: true },
                { key: 'employmentType', label: 'نوع التوظيف' },
                { key: 'directSupervisor', label: 'المرجع المباشر' },

                // الراتب والدرجة الوظيفية (مطابقة للنموذج)
                { key: 'currentSalary', label: 'الراتب الأساسي الحالي', required: true },
                { key: 'currentDegree', label: 'الدرجة الحالية', required: true },
                { key: 'currentStage', label: 'المرحلة الحالية', required: true },
                { key: 'lastPromotionDate', label: 'تاريخ آخر ترفيع (ميلادي)' },
                { key: 'nextPromotionDate', label: 'تاريخ استحقاق الترفيع القادم (ميلادي)' },
                { key: 'lastAllowanceDate', label: 'تاريخ آخر علاوة (ميلادي)' },
                { key: 'nextAllowanceDate', label: 'تاريخ استحقاق العلاوة القادمة (ميلادي)' },
                { key: 'seniority', label: 'سنوات الخدمة' },

                // التحصيل الدراسي (مطابقة للنموذج)
                { key: 'highestDegree', label: 'أعلى مؤهل علمي' },
                { key: 'specialization', label: 'التخصص' },
                { key: 'university', label: 'الجامعة/المؤسسة التعليمية' },
                { key: 'graduationYear', label: 'سنة التخرج' },
                { key: 'gpa', label: 'المعدل/التقدير' },

                // معلومات إضافية (مطابقة للنموذج)
                { key: 'languages', label: 'اللغات' },
                { key: 'skills', label: 'المهارات' },
                { key: 'trainingCourses', label: 'الدورات التدريبية' },
                { key: 'previousExperience', label: 'الخبرات السابقة' },
                { key: 'notes', label: 'ملاحظات' }
            ];

            let mappingHTML = '<h3>ربط الأعمدة: <span style="color: #ef4444;">*</span> = حقول إجبارية</h3>';

            // تجميع الحقول حسب الفئة
            const fieldGroups = {
                'البيانات الشخصية': systemFields.slice(0, 9),
                'معلومات الاتصال': systemFields.slice(9, 14),
                'البيانات الوظيفية': systemFields.slice(14, 21),
                'الراتب والدرجة': systemFields.slice(21, 29),
                'التحصيل الدراسي': systemFields.slice(29, 34),
                'معلومات إضافية': systemFields.slice(34)
            };

            Object.keys(fieldGroups).forEach(groupName => {
                mappingHTML += `<h4 style="color: #667eea; margin-top: 20px; margin-bottom: 10px;">${groupName}</h4>`;
                mappingHTML += '<div class="column-mapping">';

                fieldGroups[groupName].forEach(field => {
                    const requiredMark = field.required ? '<span style="color: #ef4444;">*</span>' : '';
                    mappingHTML += `
                        <div class="mapping-item">
                            <label>${field.label} ${requiredMark}:</label>
                            <select id="map_${field.key}">
                                <option value="">-- اختر العمود --</option>
                                ${columnHeaders.map((header, index) => {
                                    // محاولة الربط التلقائي الذكي
                                    const isMatch = header.includes(field.label) ||
                                                  header.includes(field.label.split(' ')[0]) ||
                                                  (field.key === 'fullName' && (header.includes('اسم') || header.includes('الاسم'))) ||
                                                  (field.key === 'employeeNumber' && (header.includes('رقم') && header.includes('وظيفي'))) ||
                                                  (field.key === 'currentSalary' && header.includes('راتب')) ||
                                                  (field.key === 'currentDegree' && header.includes('درجة')) ||
                                                  (field.key === 'currentStage' && header.includes('مرحلة'));
                                    return `<option value="${index}" ${isMatch ? 'selected' : ''}>${header}</option>`;
                                }).join('')}
                            </select>
                        </div>
                    `;
                });

                mappingHTML += '</div>';
            });

            columnMapping.innerHTML = mappingHTML;

            // عرض معاينة البيانات
            let previewHTML = `
                <h3>معاينة البيانات (أول 5 صفوف):</h3>
                <table class="preview-table">
                    <thead>
                        <tr>${columnHeaders.map(header => `<th>${header}</th>`).join('')}</tr>
                    </thead>
                    <tbody>
                        ${excelData.slice(0, 5).map(row => 
                            `<tr>${row.map(cell => `<td>${cell || '-'}</td>`).join('')}</tr>`
                        ).join('')}
                    </tbody>
                </table>
                <p style="margin-top: 10px; color: #666;">
                    <strong>إجمالي الصفوف:</strong> ${excelData.length}
                </p>
            `;

            previewContainer.innerHTML = previewHTML;
            document.getElementById('importBtn').style.display = 'inline-block';
        }

        // استيراد البيانات
        function importData() {
            const resultDiv = document.getElementById('importResult');
            
            try {
                // جمع خريطة الأعمدة
                const mapping = {};
                const allSystemFields = [
                    'fullName', 'employeeNumber', 'nationalId', 'birthDate', 'birthPlace', 'nationality', 'gender', 'maritalStatus', 'childrenCount',
                    'mobilePhone', 'homePhone', 'email', 'currentAddress', 'permanentAddress',
                    'jobTitle', 'jobDescription', 'workLocation', 'department', 'hireDate', 'employmentType', 'directSupervisor',
                    'currentSalary', 'currentDegree', 'currentStage', 'lastPromotionDate', 'nextPromotionDate', 'lastAllowanceDate', 'nextAllowanceDate', 'seniority',
                    'highestDegree', 'specialization', 'university', 'graduationYear', 'gpa',
                    'languages', 'skills', 'trainingCourses', 'previousExperience', 'notes'
                ];

                allSystemFields.forEach(field => {
                    const select = document.getElementById(`map_${field}`);
                    if (select && select.value !== '') {
                        mapping[field] = parseInt(select.value);
                    }
                });

                // التحقق من الحقول المطلوبة
                const requiredFields = ['fullName', 'employeeNumber', 'jobTitle', 'workLocation', 'hireDate', 'currentSalary', 'currentDegree', 'currentStage'];
                const missingFields = [];

                requiredFields.forEach(field => {
                    if (!mapping[field]) {
                        const fieldLabel = systemFields.find(f => f.key === field)?.label || field;
                        missingFields.push(fieldLabel);
                    }
                });

                if (missingFields.length > 0) {
                    throw new Error('يجب ربط الحقول الإجبارية التالية: ' + missingFields.join(', '));
                }

                // تحويل البيانات
                const employees = [];
                let successCount = 0;
                let errorCount = 0;
                const errors = [];

                excelData.forEach((row, index) => {
                    try {
                        // التحقق من الحقول الإجبارية
                        if (!row[mapping.fullName] || row[mapping.fullName].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الاسم الكامل مطلوب`);
                            errorCount++;
                            return;
                        }

                        if (!row[mapping.employeeNumber] || row[mapping.employeeNumber].toString().trim() === '') {
                            errors.push(`الصف ${index + 2}: الرقم الوظيفي مطلوب`);
                            errorCount++;
                            return;
                        }

                        // إنشاء كائن الموظف الشامل
                        const employee = {
                            // معرف فريد
                            id: Date.now() + index,
                            createdAt: new Date().toISOString(),

                            // البيانات الشخصية
                            fullName: row[mapping.fullName] || '',
                            employeeNumber: row[mapping.employeeNumber] || '',
                            nationalId: row[mapping.nationalId] || '',
                            birthDate: formatExcelDate(row[mapping.birthDate]) || '',
                            birthPlace: row[mapping.birthPlace] || '',
                            nationality: row[mapping.nationality] || '',
                            gender: row[mapping.gender] || '',
                            maritalStatus: row[mapping.maritalStatus] || '',
                            childrenCount: row[mapping.childrenCount] || '',

                            // معلومات الاتصال
                            mobilePhone: row[mapping.mobilePhone] || '',
                            homePhone: row[mapping.homePhone] || '',
                            email: row[mapping.email] || '',
                            currentAddress: row[mapping.currentAddress] || '',
                            permanentAddress: row[mapping.permanentAddress] || '',

                            // البيانات الوظيفية
                            jobTitle: row[mapping.jobTitle] || '',
                            jobDescription: row[mapping.jobDescription] || '',
                            workLocation: row[mapping.workLocation] || '',
                            department: row[mapping.department] || '',
                            hireDate: formatExcelDate(row[mapping.hireDate]) || '',
                            employmentType: row[mapping.employmentType] || '',
                            directSupervisor: row[mapping.directSupervisor] || '',

                            // الراتب والدرجة
                            currentSalary: row[mapping.currentSalary] || '0',
                            currentDegree: row[mapping.currentDegree] || 1,
                            currentStage: row[mapping.currentStage] || 1,
                            lastPromotionDate: formatExcelDate(row[mapping.lastPromotionDate]) || '',
                            nextPromotionDate: formatExcelDate(row[mapping.nextPromotionDate]) || '',
                            lastAllowanceDate: formatExcelDate(row[mapping.lastAllowanceDate]) || '',
                            nextAllowanceDate: formatExcelDate(row[mapping.nextAllowanceDate]) || '',
                            currentDueDate: formatExcelDate(row[mapping.nextAllowanceDate]) || '',
                            seniority: row[mapping.seniority] || calculateSeniority(formatExcelDate(row[mapping.hireDate])),

                            // التحصيل الدراسي
                            highestDegree: row[mapping.highestDegree] || '',
                            specialization: row[mapping.specialization] || '',
                            university: row[mapping.university] || '',
                            graduationYear: row[mapping.graduationYear] || '',
                            gpa: row[mapping.gpa] || '',

                            // معلومات إضافية
                            languages: row[mapping.languages] || '',
                            skills: row[mapping.skills] || '',
                            trainingCourses: row[mapping.trainingCourses] || '',
                            previousExperience: row[mapping.previousExperience] || '',
                            notes: row[mapping.notes] || '',

                            // للتوافق مع النظام القديم
                            name: row[mapping.fullName] || ''
                        };

                        employees.push(employee);
                        successCount++;
                    } catch (error) {
                        errors.push(`الصف ${index + 2}: ${error.message}`);
                        errorCount++;
                    }
                });

                // حفظ البيانات
                if (employees.length > 0) {
                    const existingEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
                    const allEmployees = [...existingEmployees, ...employees];
                    localStorage.setItem('employees', JSON.stringify(allEmployees));

                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ تم استيراد البيانات بنجاح!</h4>
                        <p><strong>تم استيراد:</strong> ${successCount} موظف</p>
                        ${errorCount > 0 ? `<p><strong>أخطاء:</strong> ${errorCount}</p>` : ''}
                        ${errors.length > 0 ? `<details><summary>عرض الأخطاء</summary><ul>${errors.map(error => `<li>${error}</li>`).join('')}</ul></details>` : ''}
                        <p style="margin-top: 15px;">
                            <a href="employees-list.html" class="template-btn">
                                <i class="fas fa-users"></i>
                                عرض قائمة الموظفين
                            </a>
                        </p>
                    `;
                } else {
                    throw new Error('لم يتم استيراد أي موظف');
                }

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h4>❌ خطأ في الاستيراد: ${error.message}</h4>`;
            }
        }

        // تنسيق تاريخ Excel
        function formatExcelDate(excelDate) {
            if (!excelDate) return null;
            
            // إذا كان التاريخ رقم (Excel date serial)
            if (typeof excelDate === 'number') {
                const date = new Date((excelDate - 25569) * 86400 * 1000);
                return date.toISOString().split('T')[0];
            }
            
            // إذا كان التاريخ نص
            if (typeof excelDate === 'string') {
                const date = new Date(excelDate);
                if (!isNaN(date.getTime())) {
                    return date.toISOString().split('T')[0];
                }
            }
            
            return null;
        }

        // حساب سنوات الخدمة
        function calculateSeniority(hireDate) {
            if (!hireDate) return 0;

            const hire = new Date(hireDate);
            const today = new Date();
            const years = Math.floor((today - hire) / (365.25 * 24 * 60 * 60 * 1000));

            return years >= 0 ? years : 0;
        }

        // عرض خطأ
        function showError(message) {
            const fileInfo = document.getElementById('fileInfo');
            fileInfo.style.display = 'block';
            fileInfo.innerHTML = `
                <div class="result error">
                    <h4>❌ خطأ:</h4>
                    <p>${message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
