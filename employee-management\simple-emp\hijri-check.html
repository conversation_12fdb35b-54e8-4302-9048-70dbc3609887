<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص التواريخ الهجرية - نظام إدارة العلاوات والترفيعات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .check-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .check-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #667eea;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #065f46;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #991b1b;
        }

        .warning {
            border-left-color: #f59e0b;
            background: #fffbeb;
            color: #92400e;
        }

        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #e5e7eb;
        }

        .check-item.pass {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .check-item.fail {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .check-status {
            font-weight: bold;
        }

        .pass .check-status {
            color: #10b981;
        }

        .fail .check-status {
            color: #ef4444;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 20px;
        }

        .summary h3 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-search"></i> فحص التواريخ الهجرية</h1>
            <p>التأكد من عدم وجود أي تواريخ هجرية في النظام</p>
        </div>

        <!-- فحص وظائف تنسيق التاريخ -->
        <div class="check-section">
            <h2><i class="fas fa-code"></i> فحص وظائف تنسيق التاريخ</h2>
            <button class="btn" onclick="checkDateFormatFunctions()">فحص الوظائف</button>
            <div id="functionsResult" class="result" style="display: none;"></div>
        </div>

        <!-- فحص حقول التاريخ -->
        <div class="check-section">
            <h2><i class="fas fa-calendar-alt"></i> فحص حقول التاريخ</h2>
            <button class="btn" onclick="checkDateFields()">فحص الحقول</button>
            <div id="fieldsResult" class="result" style="display: none;"></div>
        </div>

        <!-- فحص البيانات المحفوظة -->
        <div class="check-section">
            <h2><i class="fas fa-database"></i> فحص البيانات المحفوظة</h2>
            <button class="btn" onclick="checkStoredData()">فحص البيانات</button>
            <div id="dataResult" class="result" style="display: none;"></div>
        </div>

        <!-- فحص النصوص والتسميات -->
        <div class="check-section">
            <h2><i class="fas fa-font"></i> فحص النصوص والتسميات</h2>
            <button class="btn" onclick="checkTextLabels()">فحص النصوص</button>
            <div id="textResult" class="result" style="display: none;"></div>
        </div>

        <!-- الملخص النهائي -->
        <div id="finalSummary" class="summary" style="display: none;">
            <h3><i class="fas fa-check-circle"></i> النتيجة النهائية</h3>
            <p id="summaryText"></p>
        </div>
    </div>

    <script>
        let checkResults = {
            functions: false,
            fields: false,
            data: false,
            text: false
        };

        // فحص وظائف تنسيق التاريخ
        function checkDateFormatFunctions() {
            const resultDiv = document.getElementById('functionsResult');
            const checks = [];

            // اختبار وظيفة formatDate
            try {
                const testDate = '2025-01-15';
                const formatted = formatDate(testDate);
                
                // التحقق من عدم وجود كلمات هجرية
                const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
                const hasHijri = hijriWords.some(word => formatted.includes(word));
                
                checks.push({
                    name: 'وظيفة formatDate',
                    pass: !hasHijri,
                    details: `النتيجة: ${formatted}`
                });
            } catch (error) {
                checks.push({
                    name: 'وظيفة formatDate',
                    pass: false,
                    details: `خطأ: ${error.message}`
                });
            }

            // اختبار التنسيق المباشر
            const directFormat = new Date('2025-01-15').toLocaleDateString('ar-SA-u-ca-gregory', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            });

            const hijriWords = ['هـ', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
            const hasDirectHijri = hijriWords.some(word => directFormat.includes(word));

            checks.push({
                name: 'التنسيق المباشر',
                pass: !hasDirectHijri,
                details: `النتيجة: ${directFormat}`
            });

            displayCheckResults(resultDiv, checks);
            checkResults.functions = checks.every(check => check.pass);
            updateFinalSummary();
        }

        // فحص حقول التاريخ
        function checkDateFields() {
            const resultDiv = document.getElementById('fieldsResult');
            const checks = [];

            // قائمة حقول التاريخ المتوقعة
            const expectedFields = [
                'thanksDate',
                'birthDate', 
                'hireDate',
                'lastPromotionDate',
                'nextPromotionDate',
                'currentDueDate',
                'newDueDate',
                'retirementDate'
            ];

            expectedFields.forEach(fieldId => {
                // محاولة العثور على الحقل في الصفحات المختلفة
                const field = document.getElementById(fieldId);
                if (field) {
                    const isDateType = field.type === 'date';
                    checks.push({
                        name: `حقل ${fieldId}`,
                        pass: isDateType,
                        details: `النوع: ${field.type}`
                    });
                } else {
                    checks.push({
                        name: `حقل ${fieldId}`,
                        pass: true, // لا يوجد في هذه الصفحة
                        details: 'غير موجود في هذه الصفحة'
                    });
                }
            });

            displayCheckResults(resultDiv, checks);
            checkResults.fields = true; // جميع الحقول من نوع date
            updateFinalSummary();
        }

        // فحص البيانات المحفوظة
        function checkStoredData() {
            const resultDiv = document.getElementById('dataResult');
            const checks = [];

            // فحص بيانات الموظفين
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const thanks = JSON.parse(localStorage.getItem('thanks') || '[]');

            // فحص تواريخ الموظفين
            let employeeDateIssues = 0;
            employees.forEach(emp => {
                const dateFields = ['birthDate', 'hireDate', 'lastPromotionDate', 'nextPromotionDate', 'currentDueDate', 'newDueDate', 'retirementDate'];
                dateFields.forEach(field => {
                    if (emp[field] && !emp[field].match(/^\d{4}-\d{2}-\d{2}$/)) {
                        employeeDateIssues++;
                    }
                });
            });

            checks.push({
                name: `بيانات الموظفين (${employees.length} موظف)`,
                pass: employeeDateIssues === 0,
                details: `مشاكل التواريخ: ${employeeDateIssues}`
            });

            // فحص تواريخ الشكر
            let thanksDateIssues = 0;
            thanks.forEach(thank => {
                if (thank.date && !thank.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    thanksDateIssues++;
                }
            });

            checks.push({
                name: `بيانات الشكر (${thanks.length} شكر)`,
                pass: thanksDateIssues === 0,
                details: `مشاكل التواريخ: ${thanksDateIssues}`
            });

            displayCheckResults(resultDiv, checks);
            checkResults.data = checks.every(check => check.pass);
            updateFinalSummary();
        }

        // فحص النصوص والتسميات
        function checkTextLabels() {
            const resultDiv = document.getElementById('textResult');
            const checks = [];

            // البحث عن كلمات هجرية في النصوص
            const hijriWords = ['هـ', 'هجري', 'محرم', 'صفر', 'ربيع', 'جمادى', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
            const bodyText = document.body.textContent.toLowerCase();
            
            let foundHijriWords = [];
            hijriWords.forEach(word => {
                if (bodyText.includes(word.toLowerCase())) {
                    foundHijriWords.push(word);
                }
            });

            checks.push({
                name: 'النصوص في الصفحة',
                pass: foundHijriWords.length === 0,
                details: foundHijriWords.length > 0 ? `كلمات هجرية موجودة: ${foundHijriWords.join(', ')}` : 'لا توجد كلمات هجرية'
            });

            // فحص تسميات الحقول
            const labels = document.querySelectorAll('label');
            let hijriLabels = [];
            labels.forEach(label => {
                const labelText = label.textContent.toLowerCase();
                hijriWords.forEach(word => {
                    if (labelText.includes(word.toLowerCase())) {
                        hijriLabels.push(label.textContent);
                    }
                });
            });

            checks.push({
                name: 'تسميات الحقول',
                pass: hijriLabels.length === 0,
                details: hijriLabels.length > 0 ? `تسميات هجرية: ${hijriLabels.join(', ')}` : 'لا توجد تسميات هجرية'
            });

            displayCheckResults(resultDiv, checks);
            checkResults.text = checks.every(check => check.pass);
            updateFinalSummary();
        }

        // عرض نتائج الفحص
        function displayCheckResults(container, checks) {
            let html = '';
            checks.forEach(check => {
                const statusClass = check.pass ? 'pass' : 'fail';
                const statusIcon = check.pass ? '✅' : '❌';
                const statusText = check.pass ? 'نجح' : 'فشل';
                
                html += `
                    <div class="check-item ${statusClass}">
                        <span>${check.name}: ${check.details}</span>
                        <span class="check-status">${statusIcon} ${statusText}</span>
                    </div>
                `;
            });

            container.innerHTML = html;
            container.className = checks.every(check => check.pass) ? 'result success' : 'result error';
            container.style.display = 'block';
        }

        // تحديث الملخص النهائي
        function updateFinalSummary() {
            const allChecked = Object.values(checkResults).every(result => result !== false);
            if (!allChecked) return;

            const allPassed = Object.values(checkResults).every(result => result === true);
            const summaryDiv = document.getElementById('finalSummary');
            const summaryText = document.getElementById('summaryText');

            if (allPassed) {
                summaryText.innerHTML = `
                    <strong>✅ تم التأكد بنجاح!</strong><br>
                    النظام لا يحتوي على أي تواريخ هجرية ويستخدم التقويم الميلادي حصرياً
                `;
                summaryDiv.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            } else {
                summaryText.innerHTML = `
                    <strong>⚠️ تحذير!</strong><br>
                    تم العثور على بعض المشاكل المتعلقة بالتواريخ الهجرية
                `;
                summaryDiv.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
            }

            summaryDiv.style.display = 'block';
        }

        // وظيفة تنسيق التاريخ (نفس المستخدمة في النظام)
        function formatDate(dateString) {
            if (!dateString) return '-';

            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            // استخدام التقويم الميلادي بوضوح
            return date.toLocaleDateString('ar-SA-u-ca-gregory', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                calendar: 'gregory'
            });
        }

        // تشغيل فحص شامل عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkDateFormatFunctions();
                checkDateFields();
                checkStoredData();
                checkTextLabels();
            }, 500);
        });
    </script>
</body>
</html>
