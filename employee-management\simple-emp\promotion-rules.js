/**
 * ملف قواعد الترفيع والعلاوات
 * يحتوي على الخوارزميات الخاصة بتحديد العنوان الوظيفي الجديد والترفيعات
 */

// قواعد الترفيع للعناوين الوظيفية
const promotionRules = {
    // قواعد الترفيع للتدريسيين
    teaching: {
        // مدرس مساعد -> مدرس
        1: { nextId: 2, minYears: 2, requiredEducation: [4] }, // دكتوراه
        // مدرس -> أستاذ مساعد
        2: { nextId: 3, minYears: 4, requiredEducation: [4] }, // دكتوراه
        // أستاذ مساعد -> أستاذ
        3: { nextId: 4, minYears: 5, requiredEducation: [4] }, // دكتوراه
        // أستاذ (لا يوجد ترفيع بعده)
        4: { nextId: null, minYears: 0, requiredEducation: [] }
    },

    // قواعد الترفيع للفنيين
    technical: {
        // فني مختبر -> مهندس
        5: { nextId: 6, minYears: 3, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مهندس -> مبرمج
        6: { nextId: 7, minYears: 4, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مبرمج (لا يوجد ترفيع بعده)
        7: { nextId: null, minYears: 0, requiredEducation: [] }
    },

    // قواعد الترفيع للإداريين
    administrative: {
        // موظف إداري -> رئيس قسم
        8: { nextId: 9, minYears: 5, requiredEducation: [1, 2, 3, 4] }, // دبلوم أو بكالوريوس أو ماجستير أو دكتوراه
        // رئيس قسم -> مدير
        9: { nextId: 10, minYears: 7, requiredEducation: [2, 3, 4] }, // بكالوريوس أو ماجستير أو دكتوراه
        // مدير (لا يوجد ترفيع بعده)
        10: { nextId: null, minYears: 0, requiredEducation: [] }
    }
};

/**
 * تحديد العنوان الوظيفي الجديد بناءً على العنوان الوظيفي الحالي
 * @param {string} currentJobTitleId - معرف العنوان الوظيفي الحالي
 * @param {string} jobDescription - الوصف الوظيفي (تدريسي، فني، إداري)
 * @param {string} educationId - معرف التحصيل الدراسي
 * @param {Date} lastPromotionDate - تاريخ آخر ترفيع
 * @returns {Object} - معلومات العنوان الوظيفي الجديد
 */
function determineNextJobTitle(currentJobTitleId, jobDescription, educationId, lastPromotionDate) {
    // التحويل إلى أرقام
    currentJobTitleId = parseInt(currentJobTitleId);
    educationId = parseInt(educationId);

    // الحصول على قواعد الترفيع للفئة الوظيفية
    const categoryRules = promotionRules[jobDescription];
    if (!categoryRules) {
        return { id: null, name: 'غير متوفر', eligible: false, reason: 'فئة وظيفية غير معروفة' };
    }

    // الحصول على قاعدة الترفيع للعنوان الوظيفي الحالي
    const rule = categoryRules[currentJobTitleId];
    if (!rule) {
        return { id: null, name: 'غير متوفر', eligible: false, reason: 'لا توجد قاعدة ترفيع لهذا العنوان الوظيفي' };
    }

    // التحقق من وجود عنوان وظيفي تالي
    if (rule.nextId === null) {
        return { id: null, name: 'أعلى درجة وظيفية', eligible: false, reason: 'وصل إلى أعلى درجة وظيفية' };
    }

    // التحقق من التحصيل الدراسي
    if (rule.requiredEducation.length > 0 && !rule.requiredEducation.includes(educationId)) {
        return {
            id: rule.nextId,
            name: getJobTitleName(rule.nextId),
            eligible: false,
            reason: 'التحصيل الدراسي غير كافٍ للترفيع'
        };
    }

    // التحقق من المدة المطلوبة منذ آخر ترفيع
    const lastPromotionTime = new Date(lastPromotionDate).getTime();
    const currentTime = new Date().getTime();
    const yearsSinceLastPromotion = (currentTime - lastPromotionTime) / (1000 * 60 * 60 * 24 * 365);

    if (yearsSinceLastPromotion < rule.minYears) {
        return {
            id: rule.nextId,
            name: getJobTitleName(rule.nextId),
            eligible: false,
            reason: `يتطلب ${rule.minYears} سنوات منذ آخر ترفيع`
        };
    }

    // مؤهل للترفيع
    return {
        id: rule.nextId,
        name: getJobTitleName(rule.nextId),
        eligible: true,
        reason: 'مؤهل للترفيع'
    };
}

/**
 * الحصول على اسم العنوان الوظيفي من المعرف
 * @param {number} jobTitleId - معرف العنوان الوظيفي
 * @returns {string} - اسم العنوان الوظيفي
 */
function getJobTitleName(jobTitleId) {
    // قائمة العناوين الوظيفية
    const jobTitles = {
        1: 'مدرس مساعد',
        2: 'مدرس',
        3: 'أستاذ مساعد',
        4: 'أستاذ',
        5: 'فني مختبر',
        6: 'مهندس',
        7: 'مبرمج',
        8: 'موظف إداري',
        9: 'رئيس قسم',
        10: 'مدير'
    };

    return jobTitles[jobTitleId] || 'غير معروف';
}

/**
 * تحديث العنوان الوظيفي الجديد في النموذج
 */
function updateNewJobTitle() {
    const jobDescriptionSelect = document.getElementById('jobDescription');
    const jobTitleSelect = document.getElementById('jobTitle');
    const educationSelect = document.getElementById('education');
    const lastPromotionDateInput = document.getElementById('lastPromotionDate');
    const newJobTitleInput = document.getElementById('newJobTitle');

    if (!jobDescriptionSelect || !jobTitleSelect || !educationSelect || !lastPromotionDateInput || !newJobTitleInput) {
        return;
    }

    const jobDescription = jobDescriptionSelect.value;
    const currentJobTitleId = jobTitleSelect.value;
    const educationId = educationSelect.value;
    const lastPromotionDate = lastPromotionDateInput.value;

    if (!jobDescription || !currentJobTitleId || !educationId || !lastPromotionDate) {
        newJobTitleInput.value = '';
        return;
    }

    const nextJobTitle = determineNextJobTitle(currentJobTitleId, jobDescription, educationId, lastPromotionDate);

    if (nextJobTitle.eligible) {
        newJobTitleInput.value = nextJobTitle.name;
        newJobTitleInput.classList.remove('text-danger');
        newJobTitleInput.classList.add('text-success');
    } else {
        newJobTitleInput.value = `${nextJobTitle.name} (${nextJobTitle.reason})`;
        newJobTitleInput.classList.remove('text-success');
        newJobTitleInput.classList.add('text-danger');
    }
}

// إضافة مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث العنوان الوظيفي الجديد عند تحميل الصفحة
    setTimeout(function() {
        updateNewJobTitle();
    }, 500);
});
